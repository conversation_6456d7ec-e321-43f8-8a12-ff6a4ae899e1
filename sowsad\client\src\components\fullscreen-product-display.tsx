import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ShoppingCart, Heart, Star, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';

interface Product {
  id: number;
  title: string;
  price: number;
  imageUrl: string;
  description: string;
  categoryId: number;
  sellerId: number;
  sellerName: string;
  sellerVerified: boolean;
  trustScore: number;
  quantity: number;
  status: string;
  // Computed fields for compatibility
  name?: string;
  image?: string;
  category?: string;
  inStock?: boolean;
  rating?: number;
}

interface FloatingProduct {
  id: string;
  x: number;
  y: number;
  size: number;
  velocityX: number;
  velocityY: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  floatSpeed: number;
  floatOffset: number;
  product: Product;
  imageLoaded: boolean;
  imageError: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  className = ''
}) => {
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductPopup, setShowProductPopup] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const shuffleTimerRef = useRef<NodeJS.Timeout>();

  // Fetch products from database
  const productsQuery = useQuery({
    queryKey: ['/api/products'],
    enabled: true, // Always enabled to test
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const allProducts = productsQuery.data || [];

  // Debug logging
  useEffect(() => {
    console.log('🎨 Products query state:', {
      isLoading: productsQuery.isLoading,
      isError: productsQuery.isError,
      error: productsQuery.error,
      dataLength: allProducts.length,
      isVisible
    });
  }, [productsQuery.isLoading, productsQuery.isError, productsQuery.error, allProducts.length, isVisible]);

  // Helper functions for floating products
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const createFloatingProduct = useCallback((product: Product, containerWidth: number, containerHeight: number): FloatingProduct => {
    const x = random(containerWidth * 0.1, containerWidth * 0.9);
    const y = random(containerHeight * 0.1, containerHeight * 0.9);
    const size = random(60, 120);

    console.log('🎨 Creating floating product:', {
      title: product.title,
      containerWidth,
      containerHeight,
      x,
      y,
      size
    });

    return {
      id: product.id.toString(),
      x,
      y,
      size,
      velocityX: random(-2, 2), // Increased velocity for more movement
      velocityY: random(-2, 2), // Increased velocity for more movement
      rotation: 0,
      rotationSpeed: random(-0.02, 0.02),
      opacity: random(0.4, 0.7),
      floatSpeed: random(0.5, 2),
      floatOffset: random(0, Math.PI * 2),
      product: {
        ...product,
        name: product.title,
        image: product.imageUrl,
        category: `Category ${product.categoryId}`,
        inStock: product.quantity > 0 && product.status === 'active',
        rating: Math.min(5, Math.max(1, product.trustScore / 20)) // Convert trust score (0-100) to rating (1-5)
      },
      imageLoaded: false,
      imageError: false,
    };
  }, []);

  const loadRandomFloatingProducts = useCallback((count: number) => {
    console.log('🎨 loadRandomFloatingProducts called:', { count, containerExists: !!containerRef.current, productsCount: allProducts.length });

    if (!containerRef.current || allProducts.length === 0) {
      console.log('🎨 Early return - no container or no products');
      return;
    }

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    console.log('🎨 Container dimensions:', { containerWidth, containerHeight });

    // Filter products based on search query if provided
    let availableProducts = allProducts;
    if (searchQuery) {
      availableProducts = allProducts.filter(product =>
        product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.sellerName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Get products that aren't already displayed - use a Set for better performance
    setFloatingProducts(prev => {
      const currentProductIds = new Set(prev.map(fp => fp.id));
      const newProducts = availableProducts.filter(p => !currentProductIds.has(p.id.toString()));

      console.log('🎨 Available products for floating:', {
        total: availableProducts.length,
        current: currentProductIds.size,
        new: newProducts.length,
        requestedCount: count
      });

      if (newProducts.length === 0) {
        console.log('🎨 No new products available');
        return prev;
      }

      // Shuffle and take the requested count
      const shuffled = [...newProducts].sort(() => Math.random() - 0.5);
      const selectedProducts = shuffled.slice(0, Math.min(count, newProducts.length));

      const newFloatingProducts = selectedProducts.map(product =>
        createFloatingProduct(product, containerWidth, containerHeight)
      );

      console.log('🎨 Created floating products:', newFloatingProducts.length);
      const updated = [...prev, ...newFloatingProducts];
      console.log('🎨 Updated floating products count:', updated.length);
      return updated;
    });
  }, [allProducts, searchQuery, createFloatingProduct]);

  // Animation loop
  const updateFloatingProducts = useCallback(() => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    setFloatingProducts(prev => prev.map(product => {
      const newProduct = { ...product };

      // Update floating animation with gentle movement (like original p5.js version)
      newProduct.x += newProduct.velocityX;
      newProduct.y += newProduct.velocityY;

      // Add floating sine wave motion (like original)
      const time = Date.now() * 0.001; // Convert to seconds
      newProduct.y += Math.sin(time * newProduct.floatSpeed + newProduct.floatOffset) * 0.3;

      // Update rotation
      newProduct.rotation += newProduct.rotationSpeed;

      // Bounce off edges (like original)
      if (newProduct.x < newProduct.size/2 || newProduct.x > containerWidth - newProduct.size/2) {
        newProduct.velocityX *= -1;
        newProduct.x = Math.max(newProduct.size/2, Math.min(containerWidth - newProduct.size/2, newProduct.x));
      }
      if (newProduct.y < newProduct.size/2 || newProduct.y > containerHeight - newProduct.size/2) {
        newProduct.velocityY *= -1;
        newProduct.y = Math.max(newProduct.size/2, Math.min(containerHeight - newProduct.size/2, newProduct.y));
      }

      return newProduct;
    }));
  }, []);

  // Initialize floating products when component becomes visible
  useEffect(() => {
    console.log('🎨 Floating products effect:', { isVisible, productsCount: allProducts.length });

    if (isVisible && allProducts.length > 0) {
      console.log('🎨 Starting to load floating products...');
      setIsLoading(true);

      // Clear existing products first
      setFloatingProducts([]);

      // Load initial products after a short delay
      setTimeout(() => {
        console.log('🎨 Loading random floating products...');
        loadRandomFloatingProducts(Math.min(8, allProducts.length)); // Reduced from 10 to 8
        setIsLoading(false);
      }, 500);

      // Start shuffle timer (every 20 seconds)
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }

      shuffleTimerRef.current = setInterval(() => {
        setFloatingProducts(prev => {
          if (prev.length < 2) return prev;

          // Remove 2 random products
          const newProducts = [...prev];
          for (let i = 0; i < 2 && newProducts.length > 0; i++) {
            const randomIndex = Math.floor(Math.random() * newProducts.length);
            newProducts.splice(randomIndex, 1);
          }
          return newProducts;
        });

        // Add 2 new products after a short delay
        setTimeout(() => {
          loadRandomFloatingProducts(2);
        }, 100);
      }, 20000);
    } else if (!isVisible) {
      // Clear products when not visible
      setFloatingProducts([]);
      setIsLoading(false);
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    }

    return () => {
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    };
  }, [isVisible, allProducts.length, loadRandomFloatingProducts]); // Removed allProducts from deps to prevent re-runs

  // Animation loop
  useEffect(() => {
    if (!isVisible || floatingProducts.length === 0) return;

    console.log('🎨 Starting animation loop for', floatingProducts.length, 'products');

    let lastTime = 0;
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        updateFloatingProducts();
        lastTime = currentTime;
      }
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible, floatingProducts.length, updateFloatingProducts]);

  // Handle product click
  const handleProductClick = useCallback((product: Product) => {
    setSelectedProduct(product);
    setShowProductPopup(true);
  }, []);

  // Close popup
  const closeProductPopup = useCallback(() => {
    setShowProductPopup(false);
    setSelectedProduct(null);
  }, []);

  if (!isVisible) return null;

  console.log('🎨 Rendering FullscreenProductDisplay:', {
    isVisible,
    productsCount: allProducts.length,
    floatingProductsCount: floatingProducts.length,
    isLoading,
    searchQuery
  });

  return (
    <div className={`fixed inset-0 bg-black/90 backdrop-blur-sm z-[45] ${className}`}>
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 z-10 flex items-center justify-between">
        <div className="text-white">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6 text-blue-400" />
            Daswos AI
          </h1>
          {searchQuery && (
            <p className="text-gray-300 text-sm mt-1">
              Searching: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
            </p>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/10"
        >
          <X className="h-6 w-6" />
        </Button>
      </div>

      {/* Floating Products Container */}
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden z-[60]"
        style={{ pointerEvents: 'auto' }}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        )}

        {/* Floating Products - Just Images Like Original */}
        {!isLoading && floatingProducts.map((floatingProduct) => (
          <div
            key={floatingProduct.id}
            className="absolute cursor-pointer hover:scale-110 transition-transform z-[70]"
            style={{
              left: `${floatingProduct.x - floatingProduct.size / 2}px`,
              top: `${floatingProduct.y - floatingProduct.size / 2}px`,
              width: `${floatingProduct.size}px`,
              height: `${floatingProduct.size}px`,
              opacity: floatingProduct.opacity,
              transform: `rotate(${floatingProduct.rotation}rad)`,
              pointerEvents: 'auto',
            }}
            onClick={(e) => {
              e.stopPropagation();
              console.log('🎨 Product clicked:', floatingProduct.product.title);
              handleProductClick(floatingProduct.product);
            }}
          >
            {/* Glow effect behind image */}
            <div
              className="absolute inset-0 rounded-lg"
              style={{
                background: `rgba(255, 255, 255, ${floatingProduct.opacity * 0.3})`,
                filter: 'blur(5px)',
                transform: 'scale(1.1)',
              }}
            />

            {/* Product Image */}
            <img
              src={floatingProduct.product.image}
              alt={floatingProduct.product.name}
              className="w-full h-full object-cover rounded-lg border-2 border-white/30"
              style={{
                opacity: floatingProduct.imageLoaded ? 1 : 0.5,
              }}
              onLoad={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageLoaded: true } : fp
                ));
              }}
              onError={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageError: true } : fp
                ));
              }}
            />

            {/* Fallback for failed images */}
            {floatingProduct.imageError && (
              <div className="absolute inset-0 bg-blue-500/70 rounded-lg flex items-center justify-center text-white text-2xl">
                📦
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Product Detail Popup */}
      {showProductPopup && selectedProduct && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[80] flex items-center justify-center p-4">
          <div className="bg-black/90 border-2 border-cyan-400 rounded-lg p-6 max-w-md w-full text-center text-cyan-400 font-mono">
            <img
              src={selectedProduct.imageUrl}
              alt={selectedProduct.title}
              className="w-full max-h-48 object-cover rounded-md mb-4"
            />
            <h3 className="text-xl font-bold text-white mb-2">{selectedProduct.title}</h3>
            <p className="text-gray-300 text-sm mb-4">{selectedProduct.description}</p>
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(selectedProduct.rating || 4)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-400'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-300">({selectedProduct.rating || 4.0})</span>
            </div>
            <div className="text-2xl font-bold text-cyan-400 mb-4">
              ${(selectedProduct.price / 100).toFixed(2)}
            </div>
            <div className="flex gap-2 justify-center mb-4">
              <Badge variant={(selectedProduct.quantity > 0 && selectedProduct.status === 'active') ? "default" : "destructive"} className="text-xs">
                {(selectedProduct.quantity > 0 && selectedProduct.status === 'active') ? `${selectedProduct.quantity} In Stock` : "Out of Stock"}
              </Badge>
              <Badge variant="outline" className="text-xs text-gray-300 border-gray-300">
                {selectedProduct.sellerName}
              </Badge>
              {selectedProduct.sellerVerified && (
                <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                  ✓ Verified
                </Badge>
              )}
            </div>
            <div className="flex gap-2 justify-center mb-4">
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
              >
                <Heart className="h-4 w-4 mr-2" />
                Favorite
              </Button>
              <Button
                size="sm"
                disabled={!(selectedProduct.quantity > 0 && selectedProduct.status === 'active')}
                className="bg-cyan-600 hover:bg-cyan-700 text-black font-mono"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>
            </div>

            {/* Buy Now Button - Prominent */}
            <Button
              size="lg"
              disabled={!(selectedProduct.quantity > 0 && selectedProduct.status === 'active')}
              className="w-full mb-4 bg-green-600 hover:bg-green-700 text-white font-bold text-lg py-3"
            >
              💳 Buy Now - ${(selectedProduct.price / 100).toFixed(2)}
            </Button>

            <Button
              onClick={closeProductPopup}
              variant="outline"
              className="w-full bg-transparent border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black font-mono"
            >
              Close
            </Button>
          </div>
        </div>
      )}

      {/* Debug Panel */}
      <div className="absolute top-20 left-4 p-3 bg-red-600/80 rounded-lg border border-red-400 backdrop-blur-sm text-white text-xs font-mono">
        <div>Debug Info:</div>
        <div>Products API: {productsQuery.isLoading ? 'Loading...' : productsQuery.isError ? 'Error' : `${allProducts.length} products`}</div>
        <div>Floating: {floatingProducts.length} products</div>
        <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
        <div>Query: {searchQuery || 'None'}</div>
        <div>Container: {containerRef.current ? `${Math.round(containerRef.current.getBoundingClientRect().width)}x${Math.round(containerRef.current.getBoundingClientRect().height)}` : 'None'}</div>
        {productsQuery.isError && <div>Error: {String(productsQuery.error)}</div>}
        <div className="mt-2">
          <Button
            size="sm"
            onClick={() => {
              console.log('🎨 Manual trigger - products:', allProducts.length);
              if (allProducts.length > 0) {
                loadRandomFloatingProducts(5);
              }
            }}
            className="text-xs px-2 py-1 h-auto"
          >
            Load Products
          </Button>
        </div>
      </div>

      {/* AI Assistant Info - Bottom overlay */}
      <div className="absolute bottom-4 left-4 right-4 p-4 bg-blue-600/20 rounded-lg border border-blue-400/30 backdrop-blur-sm">
        <div className="flex items-center gap-2 mb-2">
          <Zap className="h-5 w-5 text-blue-400" />
          <span className="text-white font-semibold">Daswos AI Assistant</span>
        </div>
        <p className="text-gray-300 text-sm">
          Click floating products for details. Say "Daswos" followed by your command to search for products.
          {floatingProducts.length > 0 && (
            <span className="block mt-1 text-cyan-400">
              Showing {floatingProducts.length} products • Products shuffle every 20 seconds
            </span>
          )}
        </p>
      </div>
    </div>
  );
};

export default FullscreenProductDisplay;
