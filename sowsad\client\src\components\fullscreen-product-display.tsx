import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ShoppingCart, Heart, Star, Zap, RefreshCw, Search, HelpCircle, Lightbulb, Shuffle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import DaswosHologramPopup from './daswos-hologram-popup';

interface Product {
  id: number;
  title: string;
  price: number;
  imageUrl: string;
  description: string;
  categoryId: number;
  sellerId: number;
  sellerName: string;
  sellerVerified: boolean;
  trustScore: number;
  quantity: number;
  status: string;
  // Computed fields for compatibility
  name?: string;
  image?: string;
  category?: string;
  inStock?: boolean;
  rating?: number;
}

interface FloatingProduct {
  id: string;
  x: number;
  y: number;
  size: number;
  velocityX: number;
  velocityY: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  floatSpeed: number;
  floatOffset: number;
  product: Product;
  imageLoaded: boolean;
  imageError: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  onClearSearch: () => void;
  onProductPopupChange?: (isOpen: boolean) => void;
  onGoAway?: () => void;
  robotPosition?: { x: number; y: number };
  onRobotPositionChange?: (position: { x: number; y: number } | null) => void;
  lightsOn?: boolean;
  onLightsChange?: (lightsOn: boolean) => void;
  onGuessMode?: () => void;
  isGuessMode?: boolean;
  userGuess?: { x: number; y: number } | null;
  lastRobotPosition?: { x: number; y: number } | null;
  showGuessResult?: boolean;
  onScreenClick?: (event: React.MouseEvent) => void;
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  onClearSearch,
  onProductPopupChange,
  onGoAway,
  robotPosition,
  onRobotPositionChange,
  lightsOn: externalLightsOn,
  onLightsChange,
  onGuessMode,
  isGuessMode,
  userGuess,
  lastRobotPosition,
  showGuessResult,
  onScreenClick,
  className = ''
}) => {
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductPopup, setShowProductPopup] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [popupProducts, setPopupProducts] = useState<Product[]>([]);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const [lastInteractionTime, setLastInteractionTime] = useState(Date.now());
  const [showRandomProducts, setShowRandomProducts] = useState(false);
  const [internalLightsOn, setInternalLightsOn] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use external lights state if provided, otherwise use internal state
  const lightsOn = externalLightsOn !== undefined ? externalLightsOn : internalLightsOn;
  const setLightsOn = onLightsChange || setInternalLightsOn;
  const animationRef = useRef<number>();
  const shuffleTimerRef = useRef<NodeJS.Timeout>();
  const inactivityTimerRef = useRef<NodeJS.Timeout>();

  // Fetch products from database with search query (SafeSphere only) - matching main search logic exactly
  const productsQuery = useQuery({
    queryKey: ['/api/products', 'safesphere', searchQuery || 'all', true, { blockGambling: true, blockAdultContent: true }],
    queryFn: async () => {
      // Build URL exactly like main search page with SuperSafe Mode parameters
      let url = `/api/products?sphere=safesphere${searchQuery ? `&q=${encodeURIComponent(searchQuery)}` : ''}`;

      // Add SuperSafe Mode parameters (robot is always in SuperSafe mode)
      url += `&superSafeEnabled=true`;
      url += `&blockGambling=true`;
      url += `&blockAdultContent=true`;

      console.log('🔍 Fetching SafeSphere products with URL:', url);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      const data = await response.json();
      console.log('🔍 Robot search results:', {
        url,
        searchQuery,
        resultCount: data.length,
        sampleResults: data.slice(0, 3).map((p: any) => ({
          id: p.id,
          title: p.title,
          description: p.description?.substring(0, 100),
          tags: p.tags
        }))
      });
      return data;
    },
    enabled: true, // Always enabled to test
    staleTime: 0, // Don't cache results to ensure fresh data
    refetchOnMount: true, // Always refetch when component mounts
  });

  const allProducts = productsQuery.data || [];

  // Track user interactions and start inactivity timer
  const resetInactivityTimer = useCallback(() => {
    setLastInteractionTime(Date.now());
    setShowRandomProducts(false);

    // Clear existing timer
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }

    // Set new timer for 5 minutes (300 seconds)
    inactivityTimerRef.current = setTimeout(() => {
      console.log('🕐 5 minutes of inactivity - showing random products');
      setShowRandomProducts(true);
    }, 300000);
  }, []);



  // Helper functions for floating products
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const createFloatingProduct = useCallback((product: Product, containerWidth: number, containerHeight: number): FloatingProduct => {
    const x = random(containerWidth * 0.1, containerWidth * 0.9);
    const y = random(containerHeight * 0.1, containerHeight * 0.9);
    const size = random(60, 120);

    console.log('🎨 Creating floating product:', {
      title: product.title,
      containerWidth,
      containerHeight,
      x,
      y,
      size
    });

    return {
      id: product.id.toString(),
      x,
      y,
      size,
      velocityX: random(-2, 2), // Increased velocity for more movement
      velocityY: random(-2, 2), // Increased velocity for more movement
      rotation: 0,
      rotationSpeed: random(-0.02, 0.02),
      opacity: random(0.4, 0.7),
      floatSpeed: random(0.5, 2),
      floatOffset: random(0, Math.PI * 2),
      product: {
        ...product,
        name: product.title,
        image: product.imageUrl || product.image_url || product.image || '/placeholder-product.jpg',
        category: `Category ${product.categoryId}`,
        inStock: product.quantity > 0 && product.status === 'active',
        rating: Math.min(5, Math.max(1, product.trustScore / 20)) // Convert trust score (0-100) to rating (1-5)
      },
      imageLoaded: false,
      imageError: false,
    };
  }, []);

  const loadRandomFloatingProducts = useCallback((count: number, clearExisting: boolean = false, forceIgnoreSearch: boolean = false) => {
    console.log('🎨 loadRandomFloatingProducts called:', { count, clearExisting, forceIgnoreSearch, containerExists: !!containerRef.current, productsCount: allProducts.length });

    if (!containerRef.current || allProducts.length === 0) {
      console.log('🎨 Early return - no container or no products');
      return;
    }

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    console.log('🎨 Container dimensions:', { containerWidth, containerHeight });

    // Use products directly from server (server handles search filtering)
    // If forceIgnoreSearch is true, use all products regardless of search state
    let availableProducts = allProducts;

    if (forceIgnoreSearch) {
      console.log('🎲 FORCE MODE: Ignoring search state and using all products');
      // In force mode, we want to use all available products, not filtered ones
      // The allProducts should contain all SafeSphere products when no search is active
    }

    console.log('🔍 Using products from server:', {
      searchQuery: searchQuery || 'none',
      forceIgnoreSearch,
      productsCount: availableProducts.length,
      sampleProducts: availableProducts.slice(0, 3).map(p => p.title)
    });

    setFloatingProducts(prev => {
      // If clearExisting is true, start with empty array
      const currentProducts = clearExisting ? [] : prev;
      const currentProductIds = new Set(currentProducts.map(fp => fp.id));
      const newProducts = availableProducts.filter(p => !currentProductIds.has(p.id.toString()));

      console.log('🎨 Available products for floating:', {
        total: availableProducts.length,
        current: currentProductIds.size,
        new: newProducts.length,
        requestedCount: count,
        clearExisting,
        allProductsLength: allProducts.length,
        searchQuery: searchQuery || 'none'
      });

      if (newProducts.length === 0) {
        console.log('🎨 No new products available');
        return currentProducts;
      }

      // Shuffle and take the requested count
      const shuffled = [...newProducts].sort(() => Math.random() - 0.5);
      const selectedProducts = shuffled.slice(0, Math.min(count, newProducts.length));

      const newFloatingProducts = selectedProducts.map(product =>
        createFloatingProduct(product, containerWidth, containerHeight)
      );

      console.log('🎨 Created floating products:', newFloatingProducts.length);
      const updated = [...currentProducts, ...newFloatingProducts];
      console.log('🎨 Updated floating products count:', updated.length);
      return updated;
    });
  }, [allProducts, searchQuery, createFloatingProduct]);

  const handleRefresh = useCallback(() => {
    console.log('🔄 Refreshing floating products...');
    setIsLoading(true);

    setTimeout(() => {
      // Show all matching products when searching, up to 30 for random products
      const productCount = Math.min(30, allProducts.length);
      loadRandomFloatingProducts(productCount, true); // Clear existing products
      setIsLoading(false);
    }, 300);
  }, [loadRandomFloatingProducts, allProducts.length, searchQuery]);

  // Animation loop
  const updateFloatingProducts = useCallback(() => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    setFloatingProducts(prev => prev.map(product => {
      const newProduct = { ...product };

      // Update floating animation with gentle movement (like original p5.js version)
      newProduct.x += newProduct.velocityX;
      newProduct.y += newProduct.velocityY;

      // Add floating sine wave motion (like original)
      const time = Date.now() * 0.001; // Convert to seconds
      newProduct.y += Math.sin(time * newProduct.floatSpeed + newProduct.floatOffset) * 0.3;

      // Update rotation
      newProduct.rotation += newProduct.rotationSpeed;

      // Bounce off edges (like original)
      if (newProduct.x < newProduct.size/2 || newProduct.x > containerWidth - newProduct.size/2) {
        newProduct.velocityX *= -1;
        newProduct.x = Math.max(newProduct.size/2, Math.min(containerWidth - newProduct.size/2, newProduct.x));
      }
      if (newProduct.y < newProduct.size/2 || newProduct.y > containerHeight - newProduct.size/2) {
        newProduct.velocityY *= -1;
        newProduct.y = Math.max(newProduct.size/2, Math.min(containerHeight - newProduct.size/2, newProduct.y));
      }

      return newProduct;
    }));
  }, []);

  // Initialize floating products when component becomes visible
  useEffect(() => {
    console.log('🎨 Floating products effect:', { isVisible, productsCount: allProducts.length, showRandomProducts });

    if (isVisible && allProducts.length > 0) {
      console.log('🎨 Starting inactivity timer...');

      // Start inactivity timer
      resetInactivityTimer();

      // Only load products if we have a search query OR if random products should be shown
      if ((searchQuery && searchQuery.trim()) || showRandomProducts) {
        console.log('🎨 Loading floating products...');
        setIsLoading(true);

        setTimeout(() => {
          console.log('🎨 Loading floating products...');
          // Show all matching products when searching, up to 30 for random products
          const productCount = Math.min(30, allProducts.length);
          loadRandomFloatingProducts(productCount, true); // Clear existing and load appropriate count
          setIsLoading(false);
        }, 500);
      } else {
        // Clear products if no search and not showing random products
        setFloatingProducts([]);
        setIsLoading(false);
      }

      // Start shuffle timer (every 20 seconds)
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }

      shuffleTimerRef.current = setInterval(() => {
        // Only shuffle if not searching AND random products are being shown
        if ((!searchQuery || !searchQuery.trim()) && showRandomProducts) {
          setFloatingProducts(prev => {
            if (prev.length < 2) return prev;

            // Remove 2 random products
            const newProducts = [...prev];
            for (let i = 0; i < 2 && newProducts.length > 0; i++) {
              const randomIndex = Math.floor(Math.random() * newProducts.length);
              newProducts.splice(randomIndex, 1);
            }
            return newProducts;
          });

          // Add 2 new products after a short delay
          setTimeout(() => {
            loadRandomFloatingProducts(2);
          }, 100);
        }
      }, 20000);
    } else if (!isVisible) {
      // Clear products when not visible
      setFloatingProducts([]);
      setIsLoading(false);
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }
    }

    return () => {
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }
    };
  }, [isVisible, allProducts.length, loadRandomFloatingProducts, showRandomProducts, searchQuery, resetInactivityTimer]);

  // Handle showRandomProducts change to load products after inactivity
  useEffect(() => {
    if (isVisible && showRandomProducts && allProducts.length > 0 && (!searchQuery || !searchQuery.trim())) {
      console.log('🕐 Loading random products after inactivity...');
      setIsLoading(true);

      setTimeout(() => {
        loadRandomFloatingProducts(Math.min(30, allProducts.length), true);
        setIsLoading(false);
      }, 300);
    }
  }, [showRandomProducts, isVisible, allProducts.length, searchQuery, loadRandomFloatingProducts]);

  // Handle search query changes - clear products when search is cleared
  useEffect(() => {
    if (isVisible && allProducts.length > 0) {
      if (!searchQuery || !searchQuery.trim()) {
        // Search was cleared - show random products if we're supposed to show them
        console.log('🔍 Search query cleared - checking if should show random products');
        if (showRandomProducts) {
          console.log('🎲 Loading random products after search clear...');
          setIsLoading(true);
          setTimeout(() => {
            loadRandomFloatingProducts(Math.min(30, allProducts.length), true);
            setIsLoading(false);
          }, 200);
        } else {
          // Clear products if not showing random products
          console.log('🧹 Clearing products - no search and not showing random');
          setFloatingProducts([]);
        }
      }
    }
  }, [searchQuery, isVisible, allProducts.length, showRandomProducts, loadRandomFloatingProducts]);

  // DISABLED: Robot positioning when search bar opens/closes - interferes with guessing game
  // Handle robot positioning when search bar opens/closes
  // useEffect(() => {
  //   if (onRobotPositionChange) {
  //     if (showSearchBar) {
  //       // Move robot to left side when search bar is open (like in the image)
  //       const leftPosition = {
  //         x: window.innerWidth * 0.15, // 15% from left
  //         y: window.innerHeight * 0.7   // 70% from top (bottom area)
  //       };
  //       console.log('🤖 Moving robot to left position for search bar:', leftPosition);
  //       onRobotPositionChange(leftPosition);
  //     } else {
  //       // Return robot to center when search bar closes
  //       const centerPosition = {
  //         x: window.innerWidth * 0.5,  // Center horizontally
  //         y: window.innerHeight * 0.7  // 70% from top (bottom area)
  //       };
  //       console.log('🤖 Moving robot back to center position:', centerPosition);
  //       onRobotPositionChange(centerPosition);
  //     }
  //   }
  // }, [showSearchBar, onRobotPositionChange]);

  // Animation loop
  useEffect(() => {
    if (!isVisible || floatingProducts.length === 0) return;

    console.log('🎨 Starting animation loop for', floatingProducts.length, 'products');

    let lastTime = 0;
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        updateFloatingProducts();
        lastTime = currentTime;
      }
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible, floatingProducts.length, updateFloatingProducts]);

  // Handle product click
  const handleProductClick = useCallback((product: Product) => {
    resetInactivityTimer(); // Reset timer on interaction

    // Use products directly from server (already filtered by search)
    const availableProducts = allProducts;

    // Find the index of the clicked product
    const productIndex = availableProducts.findIndex(p => p.id === product.id);

    setPopupProducts(availableProducts);
    setCurrentProductIndex(productIndex >= 0 ? productIndex : 0);
    setSelectedProduct(product);
    setShowProductPopup(true);
  }, [allProducts, resetInactivityTimer]);

  // Close popup
  const closeProductPopup = useCallback(() => {
    console.log('🔄 closeProductPopup called');
    setShowProductPopup(false);
    setSelectedProduct(null);
    setPopupProducts([]);
    setCurrentProductIndex(0);
    console.log('🔄 closeProductPopup completed');
  }, []);

  // Navigate to next product
  const nextProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const nextIndex = (currentProductIndex + 1) % popupProducts.length;
      setCurrentProductIndex(nextIndex);
      setSelectedProduct(popupProducts[nextIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Navigate to previous product
  const previousProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const prevIndex = currentProductIndex === 0 ? popupProducts.length - 1 : currentProductIndex - 1;
      setCurrentProductIndex(prevIndex);
      setSelectedProduct(popupProducts[prevIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Notify parent when popup state changes
  useEffect(() => {
    if (onProductPopupChange) {
      onProductPopupChange(showProductPopup);
    }
  }, [showProductPopup, onProductPopupChange]);

  // Sync search input with searchQuery prop (for voice search compatibility)
  useEffect(() => {
    if (searchQuery && searchQuery !== searchInput) {
      console.log('🔄 Syncing searchInput with searchQuery:', searchQuery);
      setSearchInput(searchQuery);
    }
  }, [searchQuery]); // Remove searchInput from dependencies to prevent infinite loops

  // Handle buying product with DasWos coins
  const handleBuyProduct = useCallback(async (product: Product) => {
    try {
      console.log('💰 Buying product with DasWos coins:', product.title);

      // TODO: Implement actual purchase logic with DasWos coins
      // For now, show a success message
      alert(`Successfully purchased ${product.title} for $${(product.price / 100).toFixed(2)} using DasWos coins!`);

      // Close popup after successful purchase
      closeProductPopup();
    } catch (error) {
      console.error('❌ Error buying product:', error);
      alert('Failed to purchase product. Please try again.');
    }
  }, [closeProductPopup]);

  // Handle adding product to list
  const handleAddToList = useCallback(async (product: Product) => {
    try {
      console.log('📝 Adding product to list:', product.title);

      // TODO: Implement actual add to list logic
      // For now, show a simple prompt for list selection
      const listName = prompt('Enter list name (or leave empty for "Wishlist"):') || 'Wishlist';

      alert(`Added ${product.title} to "${listName}" list!`);
    } catch (error) {
      console.error('❌ Error adding to list:', error);
      alert('Failed to add product to list. Please try again.');
    }
  }, []);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    console.log('🔍 Manual search for:', query);
    if (query.trim()) {
      // Close any open product popup first
      if (showProductPopup) {
        closeProductPopup();
      }

      // Trigger a refresh with the new search query
      setIsLoading(true);

      // Keep the search input as is - don't modify it to prevent clearing
      // setSearchInput(query.trim()); // Commented out to prevent search input from being cleared

      // Dispatch search event for parent components
      window.dispatchEvent(new CustomEvent('daswosSearch', { detail: { query: query.trim() } }));

      // Refresh products with search filter
      setTimeout(() => {
        // Show all matching products when searching, up to 30 for random products
        const productCount = Math.min(30, allProducts.length);
        loadRandomFloatingProducts(productCount, true);
        setIsLoading(false);
      }, 300);
    }
    // Don't close search bar after search - keep it open for user to see their query
  }, [loadRandomFloatingProducts, allProducts.length, showProductPopup, closeProductPopup]);

  // Toggle search bar
  const toggleSearchBar = useCallback(() => {
    resetInactivityTimer(); // Reset timer on interaction
    setShowSearchBar(prev => !prev);
    // Don't clear input when opening - preserve user's search terms
    // if (!showSearchBar) {
    //   setSearchInput('');
    // }
  }, [showSearchBar, resetInactivityTimer]);

  // Close search bar
  const closeSearchBar = useCallback(() => {
    setShowSearchBar(false);
    setSearchInput('');
  }, []);

  // Handle background click to close popup
  const handleBackgroundClick = useCallback((e: React.MouseEvent) => {
    // Only close if clicking on the background itself, not on products or other elements
    if (e.target === e.currentTarget && showProductPopup) {
      console.log('🖱️ Background clicked - closing product popup');
      closeProductPopup();
    }
  }, [showProductPopup, closeProductPopup]);

  if (!isVisible) return null;

  console.log('🎨 Rendering FullscreenProductDisplay:', {
    isVisible,
    productsCount: allProducts.length,
    floatingProductsCount: floatingProducts.length,
    isLoading,
    searchQuery,
    productsQueryLoading: productsQuery.isLoading,
    productsQueryError: productsQuery.error
  });

  return (
    <div
      className={`fixed inset-0 ${lightsOn ? 'bg-gray-800/70' : 'bg-black/90'} backdrop-blur-sm z-[20] ${className} transition-colors duration-1000 ${isGuessMode ? 'cursor-crosshair' : ''}`}
      style={{ pointerEvents: 'auto' }}
      onClick={(e) => {
        // Handle guessing game clicks first
        if (onScreenClick) {
          onScreenClick(e);
        } else {
          handleBackgroundClick(e);
        }
      }}
      onMouseMove={resetInactivityTimer}
      onKeyDown={resetInactivityTimer}
      onTouchStart={resetInactivityTimer}
    >
      {/* Room Lighting Effects */}
      {lightsOn && (
        <>
          {/* Top ceiling lights */}
          <div className="absolute top-0 left-1/4 w-32 h-32 bg-yellow-300/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-0 right-1/4 w-32 h-32 bg-yellow-300/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '0.5s' }} />
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-40 h-40 bg-yellow-200/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />

          {/* Side wall lights */}
          <div className="absolute top-1/3 left-0 w-24 h-24 bg-yellow-300/15 rounded-full blur-2xl" />
          <div className="absolute top-1/3 right-0 w-24 h-24 bg-yellow-300/15 rounded-full blur-2xl" />

          {/* Ambient room glow */}
          <div className="absolute inset-0 bg-gradient-to-b from-yellow-100/5 via-transparent to-transparent pointer-events-none" />
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-100/3 via-transparent to-yellow-100/3 pointer-events-none" />
        </>
      )}
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 z-[1100] flex items-center justify-between" style={{ pointerEvents: 'auto' }}>
        <div className="text-white">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6 text-blue-400" />
            Daswos AI
          </h1>
          {searchQuery && (
            <p className="text-gray-300 text-sm mt-1">
              Searching: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
            </p>
          )}
        </div>
        <div className="flex gap-2">
          {/* Search Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('🔍 Search button clicked!');
              toggleSearchBar();
            }}
            className="text-white hover:bg-white/10 border border-white/20 bg-gray-800/50"
            title="Search products"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <Search className="h-6 w-6" />
          </Button>

          {/* Turn On Lights Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('💡 Turn on lights button clicked!');
              setLightsOn(!lightsOn);
            }}
            className={`text-white hover:bg-white/10 border border-white/20 ${lightsOn ? 'bg-yellow-600/50' : 'bg-gray-800/50'}`}
            title={lightsOn ? "Turn off lights" : "Turn on lights"}
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <Lightbulb className={`h-6 w-6 ${lightsOn ? 'text-yellow-300' : ''}`} />
          </Button>

          {/* Shuffle Products Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('🔀 Shuffle products button clicked!');

              setIsLoading(true);

              // Shuffle products based on current state
              setTimeout(() => {
                if (searchQuery && searchQuery.trim()) {
                  // If there's a search, shuffle to show all matching products
                  console.log('🔀 Shuffling search results...');
                  loadRandomFloatingProducts(Math.min(30, allProducts.length), true); // Clear existing and load all matching
                } else {
                  // If no search, shuffle to show 30 new random products
                  console.log('🔀 Shuffling to 30 new random products...');
                  loadRandomFloatingProducts(Math.min(30, allProducts.length), true, true); // Force ignore search
                }
                setIsLoading(false);
              }, 300);
            }}
            className="text-white hover:bg-white/10 border border-white/20 bg-gray-800/50"
            title="Shuffle products"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <Shuffle className="h-6 w-6" />
          </Button>

          {/* Guess Game Button - start guessing where Daswos will appear */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('🎯 Guess game button clicked!');

              if (onGuessMode) {
                onGuessMode();
              }
            }}
            className={`text-white hover:bg-white/10 border border-white/20 ${isGuessMode ? 'bg-blue-600/50' : 'bg-gray-800/50'}`}
            title="Guess where Daswos will appear next"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <HelpCircle className={`h-6 w-6 ${isGuessMode ? 'text-blue-300' : ''}`} />
          </Button>

          {/* Exit Button - triggers robot glide away effect */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('❌ Robot glide away button clicked!');
              if (onGoAway) {
                onGoAway();
              }
            }}
            className="text-white hover:bg-red-500/20 border border-red-400/30 bg-red-800/30"
            title="Robot Go Away"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <X className="h-6 w-6" />
          </Button>
        </div>
      </div>

      {/* Search Bar - Bottom of page, persistent */}
      {showSearchBar && (
        <div
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-[1110] w-96"
          style={{ pointerEvents: 'auto' }}
        >
          <div className="bg-gray-800/95 backdrop-blur-sm border border-gray-600 rounded-lg p-4 shadow-xl">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSearch(searchInput);
                  }
                  if (e.key === 'Escape') {
                    e.preventDefault();
                    e.stopPropagation();
                    closeSearchBar();
                  }
                }}
                placeholder="Search for products..."
                className="flex-1 bg-gray-700 text-white border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400"
                autoFocus
              />
              <Button
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSearch(searchInput);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
                title="Search"
              >
                <Search className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  closeSearchBar();
                }}
                className="text-gray-400 hover:text-white"
                title="Close search"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Guessing Game Visual Indicators */}
      {isGuessMode && (
        <div className="absolute inset-0 z-[1105] pointer-events-none">
          <div
            className="absolute text-center"
            style={{
              // Smart positioning - only move if robot is near center
              top: (() => {
                if (!robotPosition) {
                  console.log('🎯 No robot position - centering guessing game message');
                  return '50%';
                }

                const screenHeight = containerRef.current?.clientHeight || window.innerHeight || 800;
                const screenCenterY = screenHeight / 2;
                const robotDistanceFromCenterY = Math.abs(robotPosition.y - screenCenterY);

                console.log('🎯 Guessing Game - Robot Y distance from center:', robotDistanceFromCenterY);

                // Only move if robot is within 200px of vertical center
                if (robotDistanceFromCenterY < 200) {
                  const newPos = robotPosition.y < screenCenterY ? '75%' : '25%';
                  console.log('🎯 Robot near center vertically - moving guessing game message to:', newPos);
                  return newPos;
                }

                console.log('🎯 Robot not near center vertically - keeping guessing game message centered');
                return '50%';
              })(),
              left: (() => {
                if (!robotPosition) {
                  return '50%';
                }

                const screenWidth = containerRef.current?.clientWidth || window.innerWidth || 1200;
                const screenCenterX = screenWidth / 2;
                const robotDistanceFromCenterX = Math.abs(robotPosition.x - screenCenterX);

                console.log('🎯 Guessing Game - Robot X distance from center:', robotDistanceFromCenterX);

                // Only move if robot is within 250px of horizontal center
                if (robotDistanceFromCenterX < 250) {
                  const newPos = robotPosition.x < screenCenterX ? '75%' : '25%';
                  console.log('🎯 Robot near center horizontally - moving guessing game message to:', newPos);
                  return newPos;
                }

                console.log('🎯 Robot not near center horizontally - keeping guessing game message centered');
                return '50%';
              })(),
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div className="bg-transparent border-none rounded-lg p-6">
              <h3 className="text-white text-xl font-bold mb-2 drop-shadow-lg">🎯 Guessing Game</h3>
              <p className="text-white text-sm drop-shadow-lg">Click anywhere to guess where Daswos will appear!</p>
            </div>
          </div>
        </div>
      )}

      {/* User's Guess Marker */}
      {userGuess && (
        <div
          className="absolute z-[1106] pointer-events-none"
          style={{
            left: `${userGuess.x - 15}px`,
            top: `${userGuess.y - 15}px`,
          }}
        >
          <div className="w-8 h-8 bg-blue-500 border-2 border-blue-300 rounded-full animate-pulse flex items-center justify-center">
            <span className="text-white text-xs font-bold">🎯</span>
          </div>
          <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-blue-600/90 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            Your Guess
          </div>
        </div>
      )}

      {/* Actual Robot Position Marker */}
      {showGuessResult && lastRobotPosition && (
        <div
          className="absolute z-[1108] pointer-events-none"
          style={{
            left: `${lastRobotPosition.x - 20}px`,
            top: `${lastRobotPosition.y - 20}px`,
          }}
        >
          <div className="w-10 h-10 bg-green-500 border-3 border-green-300 rounded-full animate-pulse flex items-center justify-center shadow-lg">
            <span className="text-white text-lg font-bold">🤖</span>
          </div>
        </div>
      )}

      {/* Connection line between guess and actual position */}
      {showGuessResult && lastRobotPosition && userGuess && (
        <div
          className="absolute z-[1107] pointer-events-none"
          style={{
            left: `${lastRobotPosition.x - 40}px`,
            top: `${lastRobotPosition.y - 40}px`, // Position at robot location
          }}
        >
          {/* Connection line between guess and actual position */}
          <svg
            className="absolute top-0 left-0 pointer-events-none"
            style={{
              width: `${Math.abs(lastRobotPosition.x - userGuess.x) + 80}px`,
              height: `${Math.abs(lastRobotPosition.y - userGuess.y) + 80}px`,
              left: `${Math.min(userGuess.x, lastRobotPosition.x) - lastRobotPosition.x + 40}px`,
              top: `${Math.min(userGuess.y, lastRobotPosition.y) - lastRobotPosition.y + 40}px`,
            }}
          >
            <line
              x1={userGuess.x < lastRobotPosition.x ? 40 : Math.abs(lastRobotPosition.x - userGuess.x) + 40}
              y1={userGuess.y < lastRobotPosition.y ? 40 : Math.abs(lastRobotPosition.y - userGuess.y) + 40}
              x2={userGuess.x < lastRobotPosition.x ? Math.abs(lastRobotPosition.x - userGuess.x) + 40 : 40}
              y2={userGuess.y < lastRobotPosition.y ? Math.abs(lastRobotPosition.y - userGuess.y) + 40 : 40}
              stroke="rgba(34, 197, 94, 0.8)"
              strokeWidth="3"
              strokeDasharray="8,4"
              className="animate-pulse"
            />
            {/* Arrow pointing to Daswos robot */}
            <polygon
              points={`${userGuess.x < lastRobotPosition.x ? Math.abs(lastRobotPosition.x - userGuess.x) + 35 : 45},${userGuess.y < lastRobotPosition.y ? Math.abs(lastRobotPosition.y - userGuess.y) + 35 : 45} ${userGuess.x < lastRobotPosition.x ? Math.abs(lastRobotPosition.x - userGuess.x) + 45 : 35},${userGuess.y < lastRobotPosition.y ? Math.abs(lastRobotPosition.y - userGuess.y) + 40 : 40} ${userGuess.x < lastRobotPosition.x ? Math.abs(lastRobotPosition.x - userGuess.x) + 45 : 35},${userGuess.y < lastRobotPosition.y ? Math.abs(lastRobotPosition.y - userGuess.y) + 30 : 50}`}
              fill="rgba(34, 197, 94, 0.8)"
              className="animate-pulse"
            />
          </svg>
        </div>
      )}

      {/* Floating Products Container */}
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden z-[75]"
        style={{ pointerEvents: 'none' }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        )}

        {/* No Results Message */}
        {!isLoading && searchQuery && searchQuery.trim() && floatingProducts.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold mb-2">No Results Found</h3>
              <p className="text-lg opacity-80 mb-4">No products found for "{searchQuery}"</p>
              <p className="text-sm opacity-60">Try a different search term or browse random products</p>
            </div>
          </div>
        )}

        {/* Floating Products - Just Images Like Original */}
        {!isLoading && floatingProducts.map((floatingProduct) => (
          <div
            key={floatingProduct.id}
            className="absolute cursor-pointer hover:scale-110 transition-transform"
            style={{
              left: `${floatingProduct.x - floatingProduct.size / 2}px`,
              top: `${floatingProduct.y - floatingProduct.size / 2}px`,
              width: `${floatingProduct.size}px`,
              height: `${floatingProduct.size}px`,
              opacity: floatingProduct.opacity,
              transform: `rotate(${floatingProduct.rotation}rad)`,
              pointerEvents: 'auto',
              zIndex: 80,
            }}
            onClick={(e) => {
              e.stopPropagation();
              console.log('🎨 Product clicked:', floatingProduct.product.title);
              handleProductClick(floatingProduct.product);
            }}
          >
            {/* Glow effect behind image */}
            <div
              className="absolute inset-0 rounded-lg"
              style={{
                background: `rgba(255, 255, 255, ${floatingProduct.opacity * 0.3})`,
                filter: 'blur(5px)',
                transform: 'scale(1.1)',
              }}
            />

            {/* Product Image */}
            <img
              src={floatingProduct.product.image}
              alt={floatingProduct.product.name}
              className="w-full h-full object-cover rounded-lg border-2 border-white/30"
              style={{
                opacity: floatingProduct.imageLoaded ? 1 : 0.5,
              }}
              onLoad={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageLoaded: true } : fp
                ));
              }}
              onError={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageError: true } : fp
                ));
              }}
            />

            {/* Fallback for failed images */}
            {floatingProduct.imageError && (
              <div className="absolute inset-0 bg-blue-500/70 rounded-lg flex items-center justify-center text-white text-2xl">
                📦
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Enhanced Daswos Hologram Popup with Robot Animation */}
      <DaswosHologramPopup
        isVisible={showProductPopup && !!selectedProduct && popupProducts.length > 0 && !!robotPosition}
        selectedProduct={selectedProduct}
        popupProducts={popupProducts}
        currentProductIndex={currentProductIndex}
        robotPosition={robotPosition || { x: 0, y: 0 }}
        searchQuery={searchQuery}
        onClose={() => {
          // Only close the hologram popup, not the entire robot interface
          closeProductPopup();
        }}
        onPrevious={previousProduct}
        onNext={nextProduct}
        onBuyProduct={handleBuyProduct}
        onAddToList={handleAddToList}
      />








    </div>
  );
};

export default FullscreenProductDisplay;
