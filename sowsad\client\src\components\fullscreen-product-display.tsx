import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ShoppingCart, Heart, Star, Zap, RefreshCw, Search, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';

interface Product {
  id: number;
  title: string;
  price: number;
  imageUrl: string;
  description: string;
  categoryId: number;
  sellerId: number;
  sellerName: string;
  sellerVerified: boolean;
  trustScore: number;
  quantity: number;
  status: string;
  // Computed fields for compatibility
  name?: string;
  image?: string;
  category?: string;
  inStock?: boolean;
  rating?: number;
}

interface FloatingProduct {
  id: string;
  x: number;
  y: number;
  size: number;
  velocityX: number;
  velocityY: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  floatSpeed: number;
  floatOffset: number;
  product: Product;
  imageLoaded: boolean;
  imageError: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  onClearSearch: () => void;
  onProductPopupChange?: (isOpen: boolean) => void;
  onGoAway?: () => void;
  robotPosition?: { x: number; y: number };
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  onClearSearch,
  onProductPopupChange,
  onGoAway,
  robotPosition,
  className = ''
}) => {
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductPopup, setShowProductPopup] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [popupProducts, setPopupProducts] = useState<Product[]>([]);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const shuffleTimerRef = useRef<NodeJS.Timeout>();

  // Fetch products from database
  const productsQuery = useQuery({
    queryKey: ['/api/products?sphere=all'],
    enabled: true, // Always enabled to test
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const allProducts = productsQuery.data || [];

  // Debug logging
  useEffect(() => {
    console.log('🎨 Products query state:', {
      isLoading: productsQuery.isLoading,
      isError: productsQuery.isError,
      error: productsQuery.error,
      dataLength: allProducts.length,
      isVisible
    });
  }, [productsQuery.isLoading, productsQuery.isError, productsQuery.error, allProducts.length, isVisible]);

  // Helper functions for floating products
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const createFloatingProduct = useCallback((product: Product, containerWidth: number, containerHeight: number): FloatingProduct => {
    const x = random(containerWidth * 0.1, containerWidth * 0.9);
    const y = random(containerHeight * 0.1, containerHeight * 0.9);
    const size = random(60, 120);

    console.log('🎨 Creating floating product:', {
      title: product.title,
      containerWidth,
      containerHeight,
      x,
      y,
      size
    });

    return {
      id: product.id.toString(),
      x,
      y,
      size,
      velocityX: random(-2, 2), // Increased velocity for more movement
      velocityY: random(-2, 2), // Increased velocity for more movement
      rotation: 0,
      rotationSpeed: random(-0.02, 0.02),
      opacity: random(0.4, 0.7),
      floatSpeed: random(0.5, 2),
      floatOffset: random(0, Math.PI * 2),
      product: {
        ...product,
        name: product.title,
        image: product.imageUrl || product.image_url || product.image || '/placeholder-product.jpg',
        category: `Category ${product.categoryId}`,
        inStock: product.quantity > 0 && product.status === 'active',
        rating: Math.min(5, Math.max(1, product.trustScore / 20)) // Convert trust score (0-100) to rating (1-5)
      },
      imageLoaded: false,
      imageError: false,
    };
  }, []);

  const loadRandomFloatingProducts = useCallback((count: number, clearExisting: boolean = false) => {
    console.log('🎨 loadRandomFloatingProducts called:', { count, clearExisting, containerExists: !!containerRef.current, productsCount: allProducts.length });

    if (!containerRef.current || allProducts.length === 0) {
      console.log('🎨 Early return - no container or no products');
      return;
    }

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    console.log('🎨 Container dimensions:', { containerWidth, containerHeight });

    // Filter products based on search query if provided
    let availableProducts = allProducts;
    if (searchQuery && searchQuery.trim() !== '') {
      const searchLower = searchQuery.toLowerCase().trim();
      availableProducts = allProducts.filter(product => {
        // Check multiple possible field names for compatibility
        const title = (product.title || '').toLowerCase();
        const description = (product.description || '').toLowerCase();
        const sellerName = (product.sellerName || product.seller_name || '').toLowerCase();

        // More precise matching - check if any word in the search matches
        const searchWords = searchLower.split(' ').filter(word => word.length > 0);
        const matches = searchWords.some(word =>
          title.includes(word) ||
          description.includes(word) ||
          sellerName.includes(word)
        );

        console.log('🔍 Product search check:', {
          productTitle: product.title,
          searchQuery: searchQuery,
          searchWords,
          title,
          matches
        });

        return matches;
      });
      console.log('🔍 Filtered products by search query:', {
        searchQuery,
        searchWords: searchLower.split(' '),
        originalCount: allProducts.length,
        filteredCount: availableProducts.length
      });
    } else {
      console.log('🎨 Using all products (no search filter):', allProducts.length);
    }

    setFloatingProducts(prev => {
      // If clearExisting is true, start with empty array
      const currentProducts = clearExisting ? [] : prev;
      const currentProductIds = new Set(currentProducts.map(fp => fp.id));
      const newProducts = availableProducts.filter(p => !currentProductIds.has(p.id.toString()));

      console.log('🎨 Available products for floating:', {
        total: availableProducts.length,
        current: currentProductIds.size,
        new: newProducts.length,
        requestedCount: count,
        clearExisting,
        allProductsLength: allProducts.length,
        searchQuery: searchQuery || 'none'
      });

      if (newProducts.length === 0) {
        console.log('🎨 No new products available');
        return currentProducts;
      }

      // Shuffle and take the requested count
      const shuffled = [...newProducts].sort(() => Math.random() - 0.5);
      const selectedProducts = shuffled.slice(0, Math.min(count, newProducts.length));

      const newFloatingProducts = selectedProducts.map(product =>
        createFloatingProduct(product, containerWidth, containerHeight)
      );

      console.log('🎨 Created floating products:', newFloatingProducts.length);
      const updated = [...currentProducts, ...newFloatingProducts];
      console.log('🎨 Updated floating products count:', updated.length);
      return updated;
    });
  }, [allProducts, searchQuery, createFloatingProduct]);

  const handleRefresh = useCallback(() => {
    console.log('🔄 Refreshing floating products...');
    setIsLoading(true);

    setTimeout(() => {
      loadRandomFloatingProducts(Math.min(30, allProducts.length), true); // Clear existing products
      setIsLoading(false);
    }, 300);
  }, [loadRandomFloatingProducts, allProducts.length]);

  // Animation loop
  const updateFloatingProducts = useCallback(() => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    setFloatingProducts(prev => prev.map(product => {
      const newProduct = { ...product };

      // Update floating animation with gentle movement (like original p5.js version)
      newProduct.x += newProduct.velocityX;
      newProduct.y += newProduct.velocityY;

      // Add floating sine wave motion (like original)
      const time = Date.now() * 0.001; // Convert to seconds
      newProduct.y += Math.sin(time * newProduct.floatSpeed + newProduct.floatOffset) * 0.3;

      // Update rotation
      newProduct.rotation += newProduct.rotationSpeed;

      // Bounce off edges (like original)
      if (newProduct.x < newProduct.size/2 || newProduct.x > containerWidth - newProduct.size/2) {
        newProduct.velocityX *= -1;
        newProduct.x = Math.max(newProduct.size/2, Math.min(containerWidth - newProduct.size/2, newProduct.x));
      }
      if (newProduct.y < newProduct.size/2 || newProduct.y > containerHeight - newProduct.size/2) {
        newProduct.velocityY *= -1;
        newProduct.y = Math.max(newProduct.size/2, Math.min(containerHeight - newProduct.size/2, newProduct.y));
      }

      return newProduct;
    }));
  }, []);

  // Initialize floating products when component becomes visible
  useEffect(() => {
    console.log('🎨 Floating products effect:', { isVisible, productsCount: allProducts.length });

    if (isVisible && allProducts.length > 0) {
      console.log('🎨 Starting to load floating products...');
      setIsLoading(true);

      // Load initial products after a short delay
      setTimeout(() => {
        console.log('🎨 Loading random floating products...');
        loadRandomFloatingProducts(Math.min(30, allProducts.length), true); // Clear existing and load 30
        setIsLoading(false);
      }, 500);

      // Start shuffle timer (every 20 seconds)
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }

      shuffleTimerRef.current = setInterval(() => {
        setFloatingProducts(prev => {
          if (prev.length < 2) return prev;

          // Remove 2 random products
          const newProducts = [...prev];
          for (let i = 0; i < 2 && newProducts.length > 0; i++) {
            const randomIndex = Math.floor(Math.random() * newProducts.length);
            newProducts.splice(randomIndex, 1);
          }
          return newProducts;
        });

        // Add 2 new products after a short delay
        setTimeout(() => {
          loadRandomFloatingProducts(2);
        }, 100);
      }, 20000);
    } else if (!isVisible) {
      // Clear products when not visible
      setFloatingProducts([]);
      setIsLoading(false);
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    }

    return () => {
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    };
  }, [isVisible, allProducts.length, loadRandomFloatingProducts]); // Removed allProducts from deps to prevent re-runs

  // Animation loop
  useEffect(() => {
    if (!isVisible || floatingProducts.length === 0) return;

    console.log('🎨 Starting animation loop for', floatingProducts.length, 'products');

    let lastTime = 0;
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        updateFloatingProducts();
        lastTime = currentTime;
      }
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible, floatingProducts.length, updateFloatingProducts]);

  // Handle product click
  const handleProductClick = useCallback((product: Product) => {
    // Get available products for navigation
    let availableProducts = allProducts;
    if (searchQuery && searchQuery.trim() !== '') {
      const searchLower = searchQuery.toLowerCase();
      availableProducts = allProducts.filter(p => {
        const title = (p.title || '').toLowerCase();
        const description = (p.description || '').toLowerCase();
        const sellerName = (p.sellerName || p.seller_name || '').toLowerCase();
        const tags = Array.isArray(p.tags) ? p.tags.join(' ').toLowerCase() : '';
        return title.includes(searchLower) ||
               description.includes(searchLower) ||
               sellerName.includes(searchLower) ||
               tags.includes(searchLower);
      });
    }

    // Find the index of the clicked product
    const productIndex = availableProducts.findIndex(p => p.id === product.id);

    setPopupProducts(availableProducts);
    setCurrentProductIndex(productIndex >= 0 ? productIndex : 0);
    setSelectedProduct(product);
    setShowProductPopup(true);
  }, [allProducts, searchQuery]);

  // Close popup
  const closeProductPopup = useCallback(() => {
    setShowProductPopup(false);
    setSelectedProduct(null);
    setPopupProducts([]);
    setCurrentProductIndex(0);
  }, []);

  // Navigate to next product
  const nextProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const nextIndex = (currentProductIndex + 1) % popupProducts.length;
      setCurrentProductIndex(nextIndex);
      setSelectedProduct(popupProducts[nextIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Navigate to previous product
  const previousProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const prevIndex = currentProductIndex === 0 ? popupProducts.length - 1 : currentProductIndex - 1;
      setCurrentProductIndex(prevIndex);
      setSelectedProduct(popupProducts[prevIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Notify parent when popup state changes
  useEffect(() => {
    if (onProductPopupChange) {
      onProductPopupChange(showProductPopup);
    }
  }, [showProductPopup, onProductPopupChange]);

  // Sync search input with searchQuery prop (for voice search compatibility)
  useEffect(() => {
    if (searchQuery && searchQuery !== searchInput) {
      setSearchInput(searchQuery);
    }
  }, [searchQuery, searchInput]);

  // Handle buying product with DasWos coins
  const handleBuyProduct = useCallback(async (product: Product) => {
    try {
      console.log('💰 Buying product with DasWos coins:', product.title);

      // TODO: Implement actual purchase logic with DasWos coins
      // For now, show a success message
      alert(`Successfully purchased ${product.title} for $${(product.price / 100).toFixed(2)} using DasWos coins!`);

      // Close popup after successful purchase
      closeProductPopup();
    } catch (error) {
      console.error('❌ Error buying product:', error);
      alert('Failed to purchase product. Please try again.');
    }
  }, [closeProductPopup]);

  // Handle adding product to list
  const handleAddToList = useCallback(async (product: Product) => {
    try {
      console.log('📝 Adding product to list:', product.title);

      // TODO: Implement actual add to list logic
      // For now, show a simple prompt for list selection
      const listName = prompt('Enter list name (or leave empty for "Wishlist"):') || 'Wishlist';

      alert(`Added ${product.title} to "${listName}" list!`);
    } catch (error) {
      console.error('❌ Error adding to list:', error);
      alert('Failed to add product to list. Please try again.');
    }
  }, []);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    console.log('🔍 Manual search for:', query);
    if (query.trim()) {
      // Close any open product popup first
      if (showProductPopup) {
        closeProductPopup();
      }

      // Trigger a refresh with the new search query
      setIsLoading(true);

      // Update the search input to match
      setSearchInput(query.trim());

      // Dispatch search event for parent components
      window.dispatchEvent(new CustomEvent('daswosSearch', { detail: { query: query.trim() } }));

      // Refresh products with search filter
      setTimeout(() => {
        loadRandomFloatingProducts(Math.min(30, allProducts.length), true);
        setIsLoading(false);
      }, 300);
    }
    setShowSearchBar(false); // Close search bar after search
  }, [loadRandomFloatingProducts, allProducts.length, showProductPopup, closeProductPopup]);

  // Toggle search bar
  const toggleSearchBar = useCallback(() => {
    setShowSearchBar(prev => !prev);
    if (!showSearchBar) {
      // Clear input when opening
      setSearchInput('');
    }
  }, [showSearchBar]);

  // Close search bar
  const closeSearchBar = useCallback(() => {
    setShowSearchBar(false);
    setSearchInput('');
  }, []);

  // Handle background click to close popup
  const handleBackgroundClick = useCallback((e: React.MouseEvent) => {
    // Only close if clicking on the background itself, not on products or other elements
    if (e.target === e.currentTarget && showProductPopup) {
      console.log('🖱️ Background clicked - closing product popup');
      closeProductPopup();
    }
  }, [showProductPopup, closeProductPopup]);

  if (!isVisible) return null;

  console.log('🎨 Rendering FullscreenProductDisplay:', {
    isVisible,
    productsCount: allProducts.length,
    floatingProductsCount: floatingProducts.length,
    isLoading,
    searchQuery,
    productsQueryLoading: productsQuery.isLoading,
    productsQueryError: productsQuery.error
  });

  return (
    <div
      className={`fixed inset-0 bg-black/90 backdrop-blur-sm z-[20] ${className}`}
      style={{ pointerEvents: 'auto' }}
      onClick={handleBackgroundClick}
    >
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 z-[1100] flex items-center justify-between" style={{ pointerEvents: 'auto' }}>
        <div className="text-white">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6 text-blue-400" />
            Daswos AI
          </h1>
          {searchQuery && (
            <p className="text-gray-300 text-sm mt-1">
              Searching: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
            </p>
          )}
        </div>
        <div className="flex gap-2">
          {/* Search Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('🔍 Search button clicked!');
              toggleSearchBar();
            }}
            className="text-white hover:bg-white/10 border border-white/20 bg-gray-800/50"
            title="Search products"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <Search className="h-6 w-6" />
          </Button>

          {/* Help Button - goes back to random shuffle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('❓ Help/Clear search button clicked!');
              onClearSearch();
            }}
            className="text-white hover:bg-white/10 border border-white/20 bg-gray-800/50"
            title="Back to random products"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <HelpCircle className="h-6 w-6" />
          </Button>

          {/* Exit Button - triggers robot glide away effect */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('❌ Robot glide away button clicked!');
              if (onGoAway) {
                onGoAway();
              }
            }}
            className="text-white hover:bg-red-500/20 border border-red-400/30 bg-red-800/30"
            title="Robot Go Away"
            style={{ pointerEvents: 'auto', zIndex: 1101 }}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onMouseMove={(e) => e.stopPropagation()}
          >
            <X className="h-6 w-6" />
          </Button>
        </div>
      </div>

      {/* Search Bar - Appears when search button is clicked */}
      {showSearchBar && (
        <div
          className="absolute top-20 left-1/2 transform -translate-x-1/2 z-[1110] w-96"
          style={{ pointerEvents: 'auto' }}
        >
          <div className="bg-gray-800/95 backdrop-blur-sm border border-gray-600 rounded-lg p-4 shadow-xl">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSearch(searchInput);
                  }
                  if (e.key === 'Escape') {
                    e.preventDefault();
                    e.stopPropagation();
                    closeSearchBar();
                  }
                }}
                placeholder="Search for products..."
                className="flex-1 bg-gray-700 text-white border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400"
                autoFocus
              />
              <Button
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSearch(searchInput);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
                title="Search"
              >
                <Search className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  closeSearchBar();
                }}
                className="text-gray-400 hover:text-white"
                title="Close search"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Products Container */}
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden z-[75]"
        style={{ pointerEvents: 'none' }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        )}

        {/* Floating Products - Just Images Like Original */}
        {!isLoading && floatingProducts.map((floatingProduct) => (
          <div
            key={floatingProduct.id}
            className="absolute cursor-pointer hover:scale-110 transition-transform"
            style={{
              left: `${floatingProduct.x - floatingProduct.size / 2}px`,
              top: `${floatingProduct.y - floatingProduct.size / 2}px`,
              width: `${floatingProduct.size}px`,
              height: `${floatingProduct.size}px`,
              opacity: floatingProduct.opacity,
              transform: `rotate(${floatingProduct.rotation}rad)`,
              pointerEvents: 'auto',
              zIndex: 80,
            }}
            onClick={(e) => {
              e.stopPropagation();
              console.log('🎨 Product clicked:', floatingProduct.product.title);
              handleProductClick(floatingProduct.product);
            }}
          >
            {/* Glow effect behind image */}
            <div
              className="absolute inset-0 rounded-lg"
              style={{
                background: `rgba(255, 255, 255, ${floatingProduct.opacity * 0.3})`,
                filter: 'blur(5px)',
                transform: 'scale(1.1)',
              }}
            />

            {/* Product Image */}
            <img
              src={floatingProduct.product.image}
              alt={floatingProduct.product.name}
              className="w-full h-full object-cover rounded-lg border-2 border-white/30"
              style={{
                opacity: floatingProduct.imageLoaded ? 1 : 0.5,
              }}
              onLoad={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageLoaded: true } : fp
                ));
              }}
              onError={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageError: true } : fp
                ));
              }}
            />

            {/* Fallback for failed images */}
            {floatingProduct.imageError && (
              <div className="absolute inset-0 bg-blue-500/70 rounded-lg flex items-center justify-center text-white text-2xl">
                📦
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Hologram Product Popup - Projected from Robot's Mouth */}
      {showProductPopup && selectedProduct && popupProducts.length > 0 && robotPosition && (
        <div className="fixed inset-0 z-[90] pointer-events-none">
          {/* Hologram Projection Beam from Robot's Mouth */}
          <div
            className="absolute bg-gradient-to-t from-cyan-400/80 via-cyan-300/60 to-transparent"
            style={{
              left: `${robotPosition.x}px`,
              top: `${robotPosition.y - 50}px`, // Start from robot's mouth area
              width: '3px',
              height: `${window.innerHeight * 0.4}px`, // Beam height to center
              transform: 'translateX(-50%)',
              filter: 'blur(2px)',
              animation: 'hologramBeam 2s infinite'
            }}
          />

          {/* Multiple Projection Lines for Effect */}
          <div
            className="absolute bg-gradient-to-t from-cyan-300/40 via-cyan-200/30 to-transparent"
            style={{
              left: `${robotPosition.x - 10}px`,
              top: `${robotPosition.y - 50}px`,
              width: '1px',
              height: `${window.innerHeight * 0.35}px`,
              transform: 'translateX(-50%)',
              filter: 'blur(1px)',
              animation: 'hologramBeam 2.5s infinite'
            }}
          />
          <div
            className="absolute bg-gradient-to-t from-cyan-300/40 via-cyan-200/30 to-transparent"
            style={{
              left: `${robotPosition.x + 10}px`,
              top: `${robotPosition.y - 50}px`,
              width: '1px',
              height: `${window.innerHeight * 0.35}px`,
              transform: 'translateX(-50%)',
              filter: 'blur(1px)',
              animation: 'hologramBeam 2.2s infinite'
            }}
          />

          {/* Hologram Display - Centered on Screen */}
          <div
            className="absolute bg-gradient-to-br from-cyan-400/20 via-blue-500/15 to-purple-600/20 backdrop-blur-sm border border-cyan-400/50 rounded-lg shadow-2xl pointer-events-auto"
            style={{
              left: '50%',
              top: '30%', // Center vertically in upper portion
              width: '450px',
              height: '550px',
              transform: 'translateX(-50%) translateY(-50%)',
              background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(59, 130, 246, 0.1) 50%, rgba(147, 51, 234, 0.1) 100%)',
              boxShadow: '0 0 50px rgba(6, 182, 212, 0.3), inset 0 0 50px rgba(6, 182, 212, 0.1)',
              animation: 'hologramFlicker 3s infinite, hologramFloat 4s ease-in-out infinite'
            }}
          >
            {/* Close Button - Close Robot and Return to Page */}
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('❌ Closing Daswos robot and returning to page');
                closeProductPopup();
                // Close the entire robot interface
                if (onClose) {
                  onClose();
                }
              }}
              className="absolute top-2 right-2 z-20 w-6 h-6 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors cursor-pointer text-sm border border-cyan-400/30"
              title="Close Daswos Robot"
            >
              ✕
            </button>

            {/* Hologram Header */}
            <div className="p-4 border-b border-cyan-400/30">
              <h2 className="text-lg font-semibold text-cyan-100 text-center">
                🤖 DASWOS PRODUCT SCAN
              </h2>
              <p className="text-xs text-cyan-300/80 text-center mt-1">
                {searchQuery ? `"${searchQuery}"` : 'PRODUCT ANALYSIS'}
              </p>
            </div>

            {/* Hologram Product Display */}
            <div className="p-4">
              <div className="relative">
                {/* Hologram Navigation Arrows */}
                {popupProducts.length > 1 && (
                  <>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('⬅️ Hologram previous button clicked');
                        previousProduct();
                      }}
                      className="absolute left-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-all cursor-pointer text-lg font-bold border border-cyan-400/30"
                      title="Previous product"
                    >
                      ‹
                    </button>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('➡️ Hologram next button clicked');
                        nextProduct();
                      }}
                      className="absolute right-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-all cursor-pointer text-lg font-bold border border-cyan-400/30"
                      title="Next product"
                    >
                      ›
                    </button>
                  </>
                )}

                {/* Hologram Product Display */}
                <div className="text-center">
                  {/* Product Image with Hologram Effect */}
                  <div className="relative mb-4">
                    <div className="bg-cyan-400/10 border border-cyan-400/30 rounded-lg p-4 h-48 flex items-center justify-center relative overflow-hidden">
                      {/* Scanning Lines Effect */}
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent h-2 animate-pulse"
                           style={{ animation: 'scanLine 2s infinite linear' }} />

                      <img
                        src={selectedProduct.imageUrl || selectedProduct.image_url || selectedProduct.image || '/placeholder-product.jpg'}
                        alt={selectedProduct.title}
                        className="max-w-full max-h-full object-contain opacity-90"
                      />

                      {/* Hologram Grid Overlay */}
                      <div className="absolute inset-0 opacity-20"
                           style={{
                             backgroundImage: 'linear-gradient(rgba(6, 182, 212, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(6, 182, 212, 0.3) 1px, transparent 1px)',
                             backgroundSize: '20px 20px'
                           }} />
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-cyan-100 mb-2">
                      {selectedProduct.title}
                    </h3>
                    <p className="text-2xl font-bold text-cyan-300 mb-2">
                      ${(selectedProduct.price / 100).toFixed(2)}
                    </p>
                    <p className="text-xs text-cyan-400/80">
                      Stock: {selectedProduct.quantity} • Seller: {selectedProduct.sellerName}
                    </p>
                  </div>

                  {/* Hologram Action Buttons */}
                  <div className="space-y-2">
                    <button
                      className="w-full bg-cyan-500/30 hover:bg-cyan-500/50 border border-cyan-400/50 text-cyan-100 hover:text-white py-2 px-4 rounded transition-all font-semibold"
                      disabled={!(selectedProduct.quantity > 0 && selectedProduct.status === 'active')}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('💰 Hologram buy button clicked for:', selectedProduct.title);
                        handleBuyProduct(selectedProduct);
                      }}
                    >
                      💰 BUY WITH DASWOS COINS
                    </button>
                    <button
                      className="w-full bg-purple-500/30 hover:bg-purple-500/50 border border-purple-400/50 text-purple-100 hover:text-white py-2 px-4 rounded transition-all font-semibold"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('📝 Hologram add to list button clicked for:', selectedProduct.title);
                        handleAddToList(selectedProduct);
                      }}
                    >
                      📝 ADD TO LIST
                    </button>
                  </div>
                </div>

                {/* Hologram Product Counter */}
                {popupProducts.length > 1 && (
                  <div className="text-center mt-4 text-cyan-400/80 text-sm">
                    📊 Product {currentProductIndex + 1} of {popupProducts.length}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Debug Info - Top left */}
      <div className="absolute top-20 left-4 p-2 bg-red-600/80 rounded text-white text-xs font-mono z-50">
        <div>Products: {allProducts.length}</div>
        <div>Floating: {floatingProducts.length}</div>
        <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
        <div>Query: {searchQuery || 'None'}</div>
        <div>Query Loading: {productsQuery.isLoading ? 'Yes' : 'No'}</div>
        <div>Query Error: {productsQuery.error ? 'Yes' : 'No'}</div>
      </div>


    </div>
  );
};

export default FullscreenProductDisplay;
