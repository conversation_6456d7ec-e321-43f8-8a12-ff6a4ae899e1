import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ShoppingCart, Heart, Star, Zap, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';

interface Product {
  id: number;
  title: string;
  price: number;
  imageUrl: string;
  description: string;
  categoryId: number;
  sellerId: number;
  sellerName: string;
  sellerVerified: boolean;
  trustScore: number;
  quantity: number;
  status: string;
  // Computed fields for compatibility
  name?: string;
  image?: string;
  category?: string;
  inStock?: boolean;
  rating?: number;
}

interface FloatingProduct {
  id: string;
  x: number;
  y: number;
  size: number;
  velocityX: number;
  velocityY: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  floatSpeed: number;
  floatOffset: number;
  product: Product;
  imageLoaded: boolean;
  imageError: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  onClearSearch: () => void;
  onProductPopupChange?: (isOpen: boolean) => void;
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  onClearSearch,
  onProductPopupChange,
  className = ''
}) => {
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductPopup, setShowProductPopup] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [popupProducts, setPopupProducts] = useState<Product[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const shuffleTimerRef = useRef<NodeJS.Timeout>();

  // Fetch products from database
  const productsQuery = useQuery({
    queryKey: ['/api/products?sphere=all'],
    enabled: true, // Always enabled to test
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const allProducts = productsQuery.data || [];

  // Debug logging
  useEffect(() => {
    console.log('🎨 Products query state:', {
      isLoading: productsQuery.isLoading,
      isError: productsQuery.isError,
      error: productsQuery.error,
      dataLength: allProducts.length,
      isVisible
    });
  }, [productsQuery.isLoading, productsQuery.isError, productsQuery.error, allProducts.length, isVisible]);

  // Helper functions for floating products
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const createFloatingProduct = useCallback((product: Product, containerWidth: number, containerHeight: number): FloatingProduct => {
    const x = random(containerWidth * 0.1, containerWidth * 0.9);
    const y = random(containerHeight * 0.1, containerHeight * 0.9);
    const size = random(60, 120);

    console.log('🎨 Creating floating product:', {
      title: product.title,
      containerWidth,
      containerHeight,
      x,
      y,
      size
    });

    return {
      id: product.id.toString(),
      x,
      y,
      size,
      velocityX: random(-2, 2), // Increased velocity for more movement
      velocityY: random(-2, 2), // Increased velocity for more movement
      rotation: 0,
      rotationSpeed: random(-0.02, 0.02),
      opacity: random(0.4, 0.7),
      floatSpeed: random(0.5, 2),
      floatOffset: random(0, Math.PI * 2),
      product: {
        ...product,
        name: product.title,
        image: product.imageUrl || product.image_url || product.image || '/placeholder-product.jpg',
        category: `Category ${product.categoryId}`,
        inStock: product.quantity > 0 && product.status === 'active',
        rating: Math.min(5, Math.max(1, product.trustScore / 20)) // Convert trust score (0-100) to rating (1-5)
      },
      imageLoaded: false,
      imageError: false,
    };
  }, []);

  const loadRandomFloatingProducts = useCallback((count: number, clearExisting: boolean = false) => {
    console.log('🎨 loadRandomFloatingProducts called:', { count, clearExisting, containerExists: !!containerRef.current, productsCount: allProducts.length });

    if (!containerRef.current || allProducts.length === 0) {
      console.log('🎨 Early return - no container or no products');
      return;
    }

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    console.log('🎨 Container dimensions:', { containerWidth, containerHeight });

    // Filter products based on search query if provided
    let availableProducts = allProducts;
    if (searchQuery && searchQuery.trim() !== '') {
      const searchLower = searchQuery.toLowerCase();
      availableProducts = allProducts.filter(product => {
        // Check multiple possible field names for compatibility
        const title = (product.title || '').toLowerCase();
        const description = (product.description || '').toLowerCase();
        const sellerName = (product.sellerName || product.seller_name || '').toLowerCase();
        const tags = Array.isArray(product.tags) ? product.tags.join(' ').toLowerCase() : '';

        const matches = title.includes(searchLower) ||
                       description.includes(searchLower) ||
                       sellerName.includes(searchLower) ||
                       tags.includes(searchLower);

        console.log('🔍 Product search check:', {
          productTitle: product.title,
          searchQuery: searchQuery,
          title,
          description: description.substring(0, 50),
          sellerName,
          tags,
          matches
        });

        return matches;
      });
      console.log('🔍 Filtered products by search query:', {
        searchQuery,
        originalCount: allProducts.length,
        filteredCount: availableProducts.length,
        sampleProduct: allProducts[0] ? {
          title: allProducts[0].title,
          description: allProducts[0].description?.substring(0, 50),
          sellerName: allProducts[0].sellerName || allProducts[0].seller_name,
          tags: allProducts[0].tags
        } : 'none'
      });
    } else {
      console.log('🎨 Using all products (no search filter):', allProducts.length);
    }

    setFloatingProducts(prev => {
      // If clearExisting is true, start with empty array
      const currentProducts = clearExisting ? [] : prev;
      const currentProductIds = new Set(currentProducts.map(fp => fp.id));
      const newProducts = availableProducts.filter(p => !currentProductIds.has(p.id.toString()));

      console.log('🎨 Available products for floating:', {
        total: availableProducts.length,
        current: currentProductIds.size,
        new: newProducts.length,
        requestedCount: count,
        clearExisting,
        allProductsLength: allProducts.length,
        searchQuery: searchQuery || 'none'
      });

      if (newProducts.length === 0) {
        console.log('🎨 No new products available');
        return currentProducts;
      }

      // Shuffle and take the requested count
      const shuffled = [...newProducts].sort(() => Math.random() - 0.5);
      const selectedProducts = shuffled.slice(0, Math.min(count, newProducts.length));

      const newFloatingProducts = selectedProducts.map(product =>
        createFloatingProduct(product, containerWidth, containerHeight)
      );

      console.log('🎨 Created floating products:', newFloatingProducts.length);
      const updated = [...currentProducts, ...newFloatingProducts];
      console.log('🎨 Updated floating products count:', updated.length);
      return updated;
    });
  }, [allProducts, searchQuery, createFloatingProduct]);

  const handleRefresh = useCallback(() => {
    console.log('🔄 Refreshing floating products...');
    setIsLoading(true);

    setTimeout(() => {
      loadRandomFloatingProducts(Math.min(30, allProducts.length), true); // Clear existing products
      setIsLoading(false);
    }, 300);
  }, [loadRandomFloatingProducts, allProducts.length]);

  // Animation loop
  const updateFloatingProducts = useCallback(() => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    setFloatingProducts(prev => prev.map(product => {
      const newProduct = { ...product };

      // Update floating animation with gentle movement (like original p5.js version)
      newProduct.x += newProduct.velocityX;
      newProduct.y += newProduct.velocityY;

      // Add floating sine wave motion (like original)
      const time = Date.now() * 0.001; // Convert to seconds
      newProduct.y += Math.sin(time * newProduct.floatSpeed + newProduct.floatOffset) * 0.3;

      // Update rotation
      newProduct.rotation += newProduct.rotationSpeed;

      // Bounce off edges (like original)
      if (newProduct.x < newProduct.size/2 || newProduct.x > containerWidth - newProduct.size/2) {
        newProduct.velocityX *= -1;
        newProduct.x = Math.max(newProduct.size/2, Math.min(containerWidth - newProduct.size/2, newProduct.x));
      }
      if (newProduct.y < newProduct.size/2 || newProduct.y > containerHeight - newProduct.size/2) {
        newProduct.velocityY *= -1;
        newProduct.y = Math.max(newProduct.size/2, Math.min(containerHeight - newProduct.size/2, newProduct.y));
      }

      return newProduct;
    }));
  }, []);

  // Initialize floating products when component becomes visible
  useEffect(() => {
    console.log('🎨 Floating products effect:', { isVisible, productsCount: allProducts.length });

    if (isVisible && allProducts.length > 0) {
      console.log('🎨 Starting to load floating products...');
      setIsLoading(true);

      // Load initial products after a short delay
      setTimeout(() => {
        console.log('🎨 Loading random floating products...');
        loadRandomFloatingProducts(Math.min(30, allProducts.length), true); // Clear existing and load 30
        setIsLoading(false);
      }, 500);

      // Start shuffle timer (every 20 seconds)
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }

      shuffleTimerRef.current = setInterval(() => {
        setFloatingProducts(prev => {
          if (prev.length < 2) return prev;

          // Remove 2 random products
          const newProducts = [...prev];
          for (let i = 0; i < 2 && newProducts.length > 0; i++) {
            const randomIndex = Math.floor(Math.random() * newProducts.length);
            newProducts.splice(randomIndex, 1);
          }
          return newProducts;
        });

        // Add 2 new products after a short delay
        setTimeout(() => {
          loadRandomFloatingProducts(2);
        }, 100);
      }, 20000);
    } else if (!isVisible) {
      // Clear products when not visible
      setFloatingProducts([]);
      setIsLoading(false);
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    }

    return () => {
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    };
  }, [isVisible, allProducts.length, loadRandomFloatingProducts]); // Removed allProducts from deps to prevent re-runs

  // Animation loop
  useEffect(() => {
    if (!isVisible || floatingProducts.length === 0) return;

    console.log('🎨 Starting animation loop for', floatingProducts.length, 'products');

    let lastTime = 0;
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        updateFloatingProducts();
        lastTime = currentTime;
      }
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible, floatingProducts.length, updateFloatingProducts]);

  // Handle product click
  const handleProductClick = useCallback((product: Product) => {
    // Get available products for navigation
    let availableProducts = allProducts;
    if (searchQuery && searchQuery.trim() !== '') {
      const searchLower = searchQuery.toLowerCase();
      availableProducts = allProducts.filter(p => {
        const title = (p.title || '').toLowerCase();
        const description = (p.description || '').toLowerCase();
        const sellerName = (p.sellerName || p.seller_name || '').toLowerCase();
        const tags = Array.isArray(p.tags) ? p.tags.join(' ').toLowerCase() : '';
        return title.includes(searchLower) ||
               description.includes(searchLower) ||
               sellerName.includes(searchLower) ||
               tags.includes(searchLower);
      });
    }

    // Find the index of the clicked product
    const productIndex = availableProducts.findIndex(p => p.id === product.id);

    setPopupProducts(availableProducts);
    setCurrentProductIndex(productIndex >= 0 ? productIndex : 0);
    setSelectedProduct(product);
    setShowProductPopup(true);
  }, [allProducts, searchQuery]);

  // Close popup
  const closeProductPopup = useCallback(() => {
    setShowProductPopup(false);
    setSelectedProduct(null);
    setPopupProducts([]);
    setCurrentProductIndex(0);
  }, []);

  // Navigate to next product
  const nextProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const nextIndex = (currentProductIndex + 1) % popupProducts.length;
      setCurrentProductIndex(nextIndex);
      setSelectedProduct(popupProducts[nextIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Navigate to previous product
  const previousProduct = useCallback(() => {
    if (popupProducts.length > 0) {
      const prevIndex = currentProductIndex === 0 ? popupProducts.length - 1 : currentProductIndex - 1;
      setCurrentProductIndex(prevIndex);
      setSelectedProduct(popupProducts[prevIndex]);
    }
  }, [currentProductIndex, popupProducts]);

  // Notify parent when popup state changes
  useEffect(() => {
    if (onProductPopupChange) {
      onProductPopupChange(showProductPopup);
    }
  }, [showProductPopup, onProductPopupChange]);

  if (!isVisible) return null;

  console.log('🎨 Rendering FullscreenProductDisplay:', {
    isVisible,
    productsCount: allProducts.length,
    floatingProductsCount: floatingProducts.length,
    isLoading,
    searchQuery,
    productsQueryLoading: productsQuery.isLoading,
    productsQueryError: productsQuery.error
  });

  return (
    <div className={`fixed inset-0 bg-black/90 backdrop-blur-sm z-[20] ${className}`} style={{ pointerEvents: 'none' }}>
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 z-[100] flex items-center justify-between" style={{ pointerEvents: 'auto' }}>
        <div className="text-white">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6 text-blue-400" />
            Daswos AI
          </h1>
          {searchQuery && (
            <p className="text-gray-300 text-sm mt-1">
              Searching: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
            </p>
          )}
        </div>
        <div className="flex gap-2">
          {/* Refresh Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('🔄 Refresh button clicked!', {
                target: e.target,
                currentTarget: e.currentTarget,
                timestamp: new Date().toISOString()
              });
              // Visual feedback
              e.currentTarget.style.transform = 'scale(0.95)';
              setTimeout(() => {
                e.currentTarget.style.transform = 'scale(1)';
              }, 100);
              handleRefresh();
            }}
            className="text-white hover:bg-white/10 border border-white/20"
            title="Refresh products"
            style={{ pointerEvents: 'auto', zIndex: 101 }}
          >
            <RefreshCw className="h-6 w-6" />
          </Button>

          {/* Close Button - goes back to random shuffle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('❌ Clear search button clicked!', {
                target: e.target,
                currentTarget: e.currentTarget,
                timestamp: new Date().toISOString()
              });
              // Visual feedback
              e.currentTarget.style.transform = 'scale(0.95)';
              setTimeout(() => {
                e.currentTarget.style.transform = 'scale(1)';
              }, 100);
              onClearSearch();
            }}
            className="text-white hover:bg-white/10 border border-white/20"
            title="Back to random products"
            style={{ pointerEvents: 'auto', zIndex: 101 }}
          >
            <X className="h-6 w-6" />
          </Button>
        </div>
      </div>

      {/* Floating Products Container */}
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden z-[75]"
        style={{ pointerEvents: 'none' }}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        )}

        {/* Floating Products - Just Images Like Original */}
        {!isLoading && floatingProducts.map((floatingProduct) => (
          <div
            key={floatingProduct.id}
            className="absolute cursor-pointer hover:scale-110 transition-transform"
            style={{
              left: `${floatingProduct.x - floatingProduct.size / 2}px`,
              top: `${floatingProduct.y - floatingProduct.size / 2}px`,
              width: `${floatingProduct.size}px`,
              height: `${floatingProduct.size}px`,
              opacity: floatingProduct.opacity,
              transform: `rotate(${floatingProduct.rotation}rad)`,
              pointerEvents: 'auto',
              zIndex: 80,
            }}
            onClick={(e) => {
              e.stopPropagation();
              console.log('🎨 Product clicked:', floatingProduct.product.title);
              handleProductClick(floatingProduct.product);
            }}
          >
            {/* Glow effect behind image */}
            <div
              className="absolute inset-0 rounded-lg"
              style={{
                background: `rgba(255, 255, 255, ${floatingProduct.opacity * 0.3})`,
                filter: 'blur(5px)',
                transform: 'scale(1.1)',
              }}
            />

            {/* Product Image */}
            <img
              src={floatingProduct.product.image}
              alt={floatingProduct.product.name}
              className="w-full h-full object-cover rounded-lg border-2 border-white/30"
              style={{
                opacity: floatingProduct.imageLoaded ? 1 : 0.5,
              }}
              onLoad={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageLoaded: true } : fp
                ));
              }}
              onError={() => {
                setFloatingProducts(prev => prev.map(fp =>
                  fp.id === floatingProduct.id ? { ...fp, imageError: true } : fp
                ));
              }}
            />

            {/* Fallback for failed images */}
            {floatingProduct.imageError && (
              <div className="absolute inset-0 bg-blue-500/70 rounded-lg flex items-center justify-center text-white text-2xl">
                📦
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Product Detail Popup - New Clean Design */}
      {showProductPopup && selectedProduct && popupProducts.length > 0 && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[90] flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden relative">
            {/* Close Button */}
            <button
              onClick={closeProductPopup}
              className="absolute top-4 right-4 z-10 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              ✕
            </button>

            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 text-center">
                RESULTS FOR "{searchQuery || 'PRODUCTS'}"
              </h2>
            </div>

            {/* Product Carousel */}
            <div className="p-6">
              <div className="relative">
                {/* Navigation Arrows */}
                {popupProducts.length > 1 && (
                  <>
                    <button
                      onClick={previousProduct}
                      className="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 hover:shadow-xl transition-all"
                    >
                      ‹
                    </button>
                    <button
                      onClick={nextProduct}
                      className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 hover:shadow-xl transition-all"
                    >
                      ›
                    </button>
                  </>
                )}

                {/* Products Grid */}
                <div className="flex gap-6 justify-center min-h-[400px]">
                  {/* Current Product */}
                  <div className="flex-1 max-w-sm">
                    <div className="bg-gray-50 rounded-lg p-4 mb-4 h-64 flex items-center justify-center">
                      <img
                        src={selectedProduct.imageUrl || selectedProduct.image_url || selectedProduct.image || '/placeholder-product.jpg'}
                        alt={selectedProduct.title}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
                      {selectedProduct.title}
                    </h3>
                    <p className="text-2xl font-bold text-gray-900 text-center mb-4">
                      ${(selectedProduct.price / 100).toFixed(2)}
                    </p>

                    {/* Action Buttons */}
                    <div className="space-y-2">
                      <Button
                        className="w-full bg-black text-white hover:bg-gray-800 py-3"
                        disabled={!(selectedProduct.quantity > 0 && selectedProduct.status === 'active')}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        ADD TO BASKET
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3"
                      >
                        <Heart className="h-4 w-4 mr-2" />
                        ADD TO LIST
                      </Button>
                    </div>
                  </div>

                  {/* Next Product Preview (if available) */}
                  {popupProducts.length > 1 && (
                    <div className="flex-1 max-w-sm">
                      {(() => {
                        const nextIndex = (currentProductIndex + 1) % popupProducts.length;
                        const nextProduct = popupProducts[nextIndex];
                        return (
                          <div className="cursor-pointer" onClick={() => {
                            setCurrentProductIndex(nextIndex);
                            setSelectedProduct(nextProduct);
                          }}>
                            <div className="bg-gray-50 rounded-lg p-4 mb-4 h-64 flex items-center justify-center">
                              <img
                                src={nextProduct.imageUrl || nextProduct.image_url || nextProduct.image || '/placeholder-product.jpg'}
                                alt={nextProduct.title}
                                className="max-w-full max-h-full object-contain"
                              />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
                              {nextProduct.title}
                            </h3>
                            <p className="text-2xl font-bold text-gray-900 text-center mb-4">
                              ${(nextProduct.price / 100).toFixed(2)}
                            </p>

                            {/* Action Buttons */}
                            <div className="space-y-2">
                              <Button
                                className="w-full bg-black text-white hover:bg-gray-800 py-3"
                                disabled={!(nextProduct.quantity > 0 && nextProduct.status === 'active')}
                              >
                                <ShoppingCart className="h-4 w-4 mr-2" />
                                ADD TO BASKET
                              </Button>
                              <Button
                                variant="outline"
                                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3"
                              >
                                <Heart className="h-4 w-4 mr-2" />
                                ADD TO LIST
                              </Button>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </div>

                {/* Product Counter */}
                {popupProducts.length > 1 && (
                  <div className="text-center mt-4 text-gray-500 text-sm">
                    {currentProductIndex + 1} of {popupProducts.length} products
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Debug Info - Top left */}
      <div className="absolute top-20 left-4 p-2 bg-red-600/80 rounded text-white text-xs font-mono z-50">
        <div>Products: {allProducts.length}</div>
        <div>Floating: {floatingProducts.length}</div>
        <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
        <div>Query: {searchQuery || 'None'}</div>
        <div>Query Loading: {productsQuery.isLoading ? 'Yes' : 'No'}</div>
        <div>Query Error: {productsQuery.error ? 'Yes' : 'No'}</div>
      </div>

      {/* AI Assistant Info - Bottom overlay */}
      <div className="absolute bottom-4 left-4 right-4 p-4 bg-blue-600/20 rounded-lg border border-blue-400/30 backdrop-blur-sm">
        <div className="flex items-center gap-2 mb-2">
          <Zap className="h-5 w-5 text-blue-400" />
          <span className="text-white font-semibold">Daswos AI Assistant</span>
        </div>
        <p className="text-gray-300 text-sm">
          Click floating products for details. Say "Daswos" followed by your command to search for products.
          {floatingProducts.length > 0 && (
            <span className="block mt-1 text-cyan-400">
              Showing {floatingProducts.length} products • Products shuffle every 20 seconds
            </span>
          )}
        </p>
      </div>
    </div>
  );
};

export default FullscreenProductDisplay;
