import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ShoppingCart, Heart, Star, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';

interface Product {
  id: string;
  name: string;
  title?: string;
  price: number;
  image: string;
  image_url?: string;
  rating?: number;
  description: string;
  category: string;
  inStock: boolean;
  in_stock?: boolean;
}

interface FloatingProduct {
  id: string;
  x: number;
  y: number;
  size: number;
  velocityX: number;
  velocityY: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  floatSpeed: number;
  floatOffset: number;
  product: Product;
  imageLoaded: boolean;
  imageError: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  className = ''
}) => {
  const [floatingProducts, setFloatingProducts] = useState<FloatingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductPopup, setShowProductPopup] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const shuffleTimerRef = useRef<NodeJS.Timeout>();

  // Fetch products from database
  const productsQuery = useQuery({
    queryKey: ['/api/products'],
    enabled: isVisible,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const allProducts = productsQuery.data || [];

  // Helper functions for floating products
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const createFloatingProduct = useCallback((product: Product, containerWidth: number, containerHeight: number): FloatingProduct => {
    return {
      id: product.id,
      x: random(containerWidth * 0.1, containerWidth * 0.9),
      y: random(containerHeight * 0.1, containerHeight * 0.9),
      size: random(60, 120),
      velocityX: random(-1, 1),
      velocityY: random(-1, 1),
      rotation: 0,
      rotationSpeed: random(-0.02, 0.02),
      opacity: random(0.4, 0.7),
      floatSpeed: random(0.5, 2),
      floatOffset: random(0, Math.PI * 2),
      product: {
        ...product,
        name: product.name || product.title || 'Unknown Product',
        image: product.image || product.image_url || '/api/placeholder/200/200',
        inStock: product.inStock ?? product.in_stock ?? true,
        rating: product.rating || 4.0
      },
      imageLoaded: false,
      imageError: false,
    };
  }, []);

  const loadRandomFloatingProducts = useCallback((count: number) => {
    if (!containerRef.current || allProducts.length === 0) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    // Filter products based on search query if provided
    let availableProducts = allProducts;
    if (searchQuery) {
      availableProducts = allProducts.filter(product =>
        (product.title || product.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.category || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.description || '').toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Get products that aren't already displayed
    const currentProductIds = floatingProducts.map(fp => fp.product.id);
    const newProducts = availableProducts.filter(p => !currentProductIds.includes(p.id));

    if (newProducts.length === 0) return;

    // Shuffle and take the requested count
    const shuffled = [...newProducts].sort(() => Math.random() - 0.5);
    const selectedProducts = shuffled.slice(0, Math.min(count, newProducts.length));

    const newFloatingProducts = selectedProducts.map(product =>
      createFloatingProduct(product, containerWidth, containerHeight)
    );

    setFloatingProducts(prev => [...prev, ...newFloatingProducts]);
  }, [allProducts, searchQuery, floatingProducts, createFloatingProduct]);

  // Animation loop
  const updateFloatingProducts = useCallback(() => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    setFloatingProducts(prev => prev.map(product => {
      const newProduct = { ...product };

      // Update floating animation with gentle movement
      newProduct.x += newProduct.velocityX;
      newProduct.y += newProduct.velocityY;
      newProduct.y += Math.sin(Date.now() * 0.001 * newProduct.floatSpeed + newProduct.floatOffset) * 0.3;
      newProduct.rotation += newProduct.rotationSpeed;

      // Bounce off edges
      if (newProduct.x < newProduct.size/2 || newProduct.x > containerWidth - newProduct.size/2) {
        newProduct.velocityX *= -1;
        newProduct.x = Math.max(newProduct.size/2, Math.min(containerWidth - newProduct.size/2, newProduct.x));
      }
      if (newProduct.y < newProduct.size/2 || newProduct.y > containerHeight - newProduct.size/2) {
        newProduct.velocityY *= -1;
        newProduct.y = Math.max(newProduct.size/2, Math.min(containerHeight - newProduct.size/2, newProduct.y));
      }

      return newProduct;
    }));
  }, []);

  // Initialize floating products when component becomes visible
  useEffect(() => {
    if (isVisible && allProducts.length > 0) {
      setIsLoading(true);

      // Clear existing products
      setFloatingProducts([]);

      // Load initial products after a short delay
      setTimeout(() => {
        loadRandomFloatingProducts(Math.min(10, allProducts.length));
        setIsLoading(false);
      }, 500);

      // Start shuffle timer (every 20 seconds)
      shuffleTimerRef.current = setInterval(() => {
        setFloatingProducts(prev => {
          if (prev.length < 2) return prev;

          // Remove 2 random products
          const newProducts = [...prev];
          for (let i = 0; i < 2 && newProducts.length > 0; i++) {
            const randomIndex = Math.floor(Math.random() * newProducts.length);
            newProducts.splice(randomIndex, 1);
          }
          return newProducts;
        });

        // Add 2 new products after a short delay
        setTimeout(() => {
          loadRandomFloatingProducts(2);
        }, 100);
      }, 20000);
    }

    return () => {
      if (shuffleTimerRef.current) {
        clearInterval(shuffleTimerRef.current);
      }
    };
  }, [isVisible, allProducts, loadRandomFloatingProducts]);

  // Animation loop
  useEffect(() => {
    if (!isVisible) return;

    const animate = () => {
      updateFloatingProducts();
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible, updateFloatingProducts]);

  // Handle product click
  const handleProductClick = useCallback((product: Product) => {
    setSelectedProduct(product);
    setShowProductPopup(true);
  }, []);

  // Close popup
  const closeProductPopup = useCallback(() => {
    setShowProductPopup(false);
    setSelectedProduct(null);
  }, []);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 bg-black/90 backdrop-blur-sm z-[100] ${className}`}>
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 z-10 flex items-center justify-between">
        <div className="text-white">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6 text-blue-400" />
            Daswos AI
          </h1>
          {searchQuery && (
            <p className="text-gray-300 text-sm mt-1">
              Searching: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
            </p>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/10"
        >
          <X className="h-6 w-6" />
        </Button>
      </div>

      {/* Floating Products Container */}
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden"
        style={{ pointerEvents: 'auto' }}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        )}

        {/* Floating Products */}
        {!isLoading && floatingProducts.map((floatingProduct) => (
          <div
            key={floatingProduct.id}
            className="absolute cursor-pointer transition-transform hover:scale-110"
            style={{
              left: `${floatingProduct.x - floatingProduct.size / 2}px`,
              top: `${floatingProduct.y - floatingProduct.size / 2}px`,
              width: `${floatingProduct.size}px`,
              height: `${floatingProduct.size}px`,
              opacity: floatingProduct.opacity,
              transform: `rotate(${floatingProduct.rotation}rad)`,
              pointerEvents: 'auto',
            }}
            onClick={() => handleProductClick(floatingProduct.product)}
          >
            <div className="relative w-full h-full rounded-lg overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <img
                src={floatingProduct.product.image}
                alt={floatingProduct.product.name}
                className="w-full h-full object-cover"
                onLoad={() => {
                  setFloatingProducts(prev => prev.map(fp =>
                    fp.id === floatingProduct.id ? { ...fp, imageLoaded: true } : fp
                  ));
                }}
                onError={() => {
                  setFloatingProducts(prev => prev.map(fp =>
                    fp.id === floatingProduct.id ? { ...fp, imageError: true } : fp
                  ));
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-1 left-1 right-1 text-white text-xs">
                <p className="font-semibold truncate">{floatingProduct.product.name}</p>
                <p className="text-green-400 font-bold">${floatingProduct.product.price}</p>
              </div>
              {/* Glow effect */}
              <div className="absolute inset-0 rounded-lg shadow-[0_0_20px_rgba(78,205,196,0.3)]" />
            </div>
          </div>
        ))}
      </div>

      {/* Product Detail Popup */}
      {showProductPopup && selectedProduct && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[110] flex items-center justify-center p-4">
          <div className="bg-black/90 border-2 border-cyan-400 rounded-lg p-6 max-w-md w-full text-center text-cyan-400 font-mono">
            <img
              src={selectedProduct.image}
              alt={selectedProduct.name}
              className="w-full max-h-48 object-cover rounded-md mb-4"
            />
            <h3 className="text-xl font-bold text-white mb-2">{selectedProduct.name}</h3>
            <p className="text-gray-300 text-sm mb-4">{selectedProduct.description}</p>
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(selectedProduct.rating || 4)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-400'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-300">({selectedProduct.rating || 4.0})</span>
            </div>
            <div className="text-2xl font-bold text-cyan-400 mb-4">
              ${selectedProduct.price}
            </div>
            <div className="flex gap-2 justify-center mb-4">
              <Badge variant={selectedProduct.inStock ? "default" : "destructive"} className="text-xs">
                {selectedProduct.inStock ? "In Stock" : "Out of Stock"}
              </Badge>
              <Badge variant="outline" className="text-xs text-gray-300 border-gray-300">
                {selectedProduct.category}
              </Badge>
            </div>
            <div className="flex gap-2 justify-center">
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
              >
                <Heart className="h-4 w-4 mr-2" />
                Favorite
              </Button>
              <Button
                size="sm"
                disabled={!selectedProduct.inStock}
                className="bg-cyan-600 hover:bg-cyan-700 text-black font-mono"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>
            </div>
            <Button
              onClick={closeProductPopup}
              className="mt-4 bg-cyan-400 text-black hover:bg-cyan-300 font-mono"
            >
              Close
            </Button>
          </div>
        </div>
      )}

      {/* AI Assistant Info - Bottom overlay */}
      <div className="absolute bottom-4 left-4 right-4 p-4 bg-blue-600/20 rounded-lg border border-blue-400/30 backdrop-blur-sm">
        <div className="flex items-center gap-2 mb-2">
          <Zap className="h-5 w-5 text-blue-400" />
          <span className="text-white font-semibold">Daswos AI Assistant</span>
        </div>
        <p className="text-gray-300 text-sm">
          Click floating products for details. Say "Daswos" followed by your command to search for products.
          {floatingProducts.length > 0 && (
            <span className="block mt-1 text-cyan-400">
              Showing {floatingProducts.length} products • Products shuffle every 20 seconds
            </span>
          )}
        </p>
      </div>
    </div>
  );
};

export default FullscreenProductDisplay;
