.daswos-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #000;
  perspective: 1000px;
}

.animation-area {
  position: relative;
  width: 100%;
  max-width: 800px;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.daswos-image {
  height: 300px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.daswos-image.front:hover {
  transform: scale(1.05);
}

.daswos-turning {
  transition: transform 1s ease;
  transform-style: preserve-3d;
}

.hologram-container {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.hologram-projection {
  position: absolute;
  left: 120px;
  width: 400px;
  height: 300px;
  background-color: rgba(0, 157, 255, 0.1);
  border: 2px solid #00c3ff;
  border-radius: 10px;
  box-shadow: 0 0 20px #00c3ff, inset 0 0 20px #00c3ff;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 1s ease;
}

.hologram-content {
  color: #00c3ff;
  text-align: center;
  font-family: 'Arial', sans-serif;
  text-shadow: 0 0 10px #00c3ff;
}

.hologram-content h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  letter-spacing: 3px;
}

.hologram-content p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.hologram-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    rgba(0, 195, 255, 0.1) 0px,
    rgba(0, 195, 255, 0.1) 1px,
    transparent 1px,
    transparent 5px
  );
  pointer-events: none;
  opacity: 0.5;
}

.instruction-text {
  position: absolute;
  bottom: 20px;
  color: #fff;
  font-family: 'Arial', sans-serif;
  font-size: 1rem;
  opacity: 0.7;
}

/* Beam effect from eyes to hologram */
.hologram-container::before {
  content: '';
  position: absolute;
  left: 80px;
  top: 120px;
  width: 40px;
  height: 5px;
  background: linear-gradient(90deg, #00c3ff, rgba(0, 195, 255, 0.1));
  box-shadow: 0 0 10px #00c3ff;
  transform: rotate(-5deg);
  z-index: 1;
}

.hologram-container::after {
  content: '';
  position: absolute;
  left: 80px;
  top: 140px;
  width: 40px;
  height: 5px;
  background: linear-gradient(90deg, #00c3ff, rgba(0, 195, 255, 0.1));
  box-shadow: 0 0 10px #00c3ff;
  transform: rotate(5deg);
  z-index: 1;
}
