export interface Position {
  x: number;
  y: number;
}

export interface PositionResponse {
  position: Position;
  screenWidth: number;
  screenHeight: number;
  timestamp: string;
  source: 'database' | 'random'; // Indicates whether position came from database or random generation
}

export interface AlgorithmAssignment {
  id: number;
  algorithmId: number;
  currentPositionIndex: number;
  assignedAt: string;
  expiresAt: string;
  isActive: boolean;
}

export interface AlgorithmStats {
  totalAlgorithms: number;
  activeAssignments: number;
  lastRotation: string | null;
}

export class RobotPositioningAPI {
  private static baseUrl = '/api/robot-positioning';

  /**
   * Get the next position for the current user
   */
  static async getNextPosition(screenWidth?: number, screenHeight?: number): Promise<Position | null> {
    try {
      const params = new URLSearchParams();
      if (screenWidth) params.append('screenWidth', screenWidth.toString());
      if (screenHeight) params.append('screenHeight', screenHeight.toString());

      const response = await fetch(`${this.baseUrl}/next-position?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('🔐 User not authenticated for positioning');
          return null;
        }
        if (response.status === 404) {
          console.log('📍 No position available');
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: PositionResponse = await response.json();
      console.log(`🎯 Received position from API (${data.source}):`, data.position);
      return data.position;

    } catch (error) {
      console.error('❌ Error fetching next position:', error);
      return null;
    }
  }

  /**
   * Get current algorithm assignment for the user
   */
  static async getUserAlgorithm(): Promise<AlgorithmAssignment | null> {
    try {
      const response = await fetch(`${this.baseUrl}/user-algorithm`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('🔐 User not authenticated for algorithm info');
          return null;
        }
        if (response.status === 404) {
          console.log('📋 No algorithm assignment found');
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.assignment;

    } catch (error) {
      console.error('❌ Error fetching user algorithm:', error);
      return null;
    }
  }

  /**
   * Ensure current user has an algorithm assignment
   */
  static async ensureAssignment(): Promise<AlgorithmAssignment | null> {
    try {
      const response = await fetch(`${this.baseUrl}/ensure-assignment`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('🔐 User not authenticated for assignment');
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Algorithm assignment ensured:', data.assignment);
      return data.assignment;

    } catch (error) {
      console.error('❌ Error ensuring assignment:', error);
      return null;
    }
  }

  /**
   * Get algorithm statistics (admin only)
   */
  static async getStats(): Promise<AlgorithmStats | null> {
    try {
      const response = await fetch(`${this.baseUrl}/stats`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          console.log('🚫 Admin access required for stats');
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AlgorithmStats = await response.json();
      return data;

    } catch (error) {
      console.error('❌ Error fetching algorithm stats:', error);
      return null;
    }
  }

  /**
   * Manually trigger algorithm rotation (admin only)
   */
  static async triggerRotation(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/rotate`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          console.log('🚫 Admin access required for rotation');
          return false;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Algorithm rotation triggered:', data.message);
      return true;

    } catch (error) {
      console.error('❌ Error triggering rotation:', error);
      return false;
    }
  }

  /**
   * Create a new algorithm (admin only)
   */
  static async createAlgorithm(
    name: string,
    options: {
      screenWidth?: number;
      screenHeight?: number;
      totalPositions?: number;
      minDistance?: number;
      safeMargin?: number;
    } = {}
  ): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/create-algorithm`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          ...options
        }),
      });

      if (!response.ok) {
        if (response.status === 403) {
          console.log('🚫 Admin access required for algorithm creation');
          return false;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Algorithm created:', data.algorithm);
      return true;

    } catch (error) {
      console.error('❌ Error creating algorithm:', error);
      return false;
    }
  }
}
