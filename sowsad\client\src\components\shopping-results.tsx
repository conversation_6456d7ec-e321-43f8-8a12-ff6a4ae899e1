import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import CarouselSearchResults from '@/components/carousel-search-results';
import { Product } from '@shared/schema';

interface ShoppingResultsProps {
  searchQuery: string;
  sphere: 'safesphere' | 'opensphere';
  className?: string;
  aiModeEnabled?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
}

const ShoppingResults: React.FC<ShoppingResultsProps> = ({
  searchQuery,
  sphere,
  className = '',
  aiModeEnabled = false,
  onCurrentProductChange
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);

  // Fetch products based on search query with SuperSafe parameters (matching other search implementations)
  const { data, isLoading: queryLoading, error } = useQuery<Product[]>({
    queryKey: ['/api/products', searchQuery, sphere, true, { blockGambling: true, blockAdultContent: true }],
    queryFn: async () => {
      // Build URL exactly like main search and robot search
      let url = `/api/products?q=${encodeURIComponent(searchQuery)}&sphere=${sphere}`;

      // Add SuperSafe Mode parameters (home page search should also be SuperSafe)
      url += `&superSafeEnabled=true`;
      url += `&blockGambling=true`;
      url += `&blockAdultContent=true`;

      console.log('🔍 Home page shopping results URL:', url);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      const data = await response.json();
      console.log('🔍 Home page shopping results:', {
        url,
        searchQuery,
        sphere,
        resultCount: data.length,
        sampleResults: data.slice(0, 3).map((p: any) => ({
          id: p.id,
          title: p.title,
          description: p.description?.substring(0, 100),
          tags: p.tags
        }))
      });
      return data;
    },
    enabled: !!searchQuery,
    staleTime: 0, // Always consider data stale for real-time inventory updates
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus to catch inventory changes
    refetchInterval: 10000, // Refetch every 10 seconds for real-time updates
  });

  useEffect(() => {
    if (data) {
      // Remove duplicates based on product ID at the frontend level as well
      const uniqueProducts = Array.from(new Map(data.map(product => [product.id, product])).values());
      console.log(`🔍 ShoppingResults: Received ${data.length} products, after deduplication: ${uniqueProducts.length}`);
      setProducts(uniqueProducts);
      setIsLoading(false);
    } else if (!queryLoading) {
      setIsLoading(false);
    }
  }, [data, queryLoading]);

  if (isLoading || queryLoading) {
    return (
      <div className={`w-full flex justify-center items-center py-8 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading products...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full text-center py-8 ${className}`}>
        <p className="text-red-500">Error loading products. Please try again.</p>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className={`w-full text-center py-8 ${className}`}>
        <p className="text-gray-600 dark:text-gray-400">No products found for "{searchQuery}".</p>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="bg-gray-100 dark:bg-gray-800 py-5 px-5 rounded-lg shadow-sm">
        <h2 className="text-base font-semibold mb-4 text-center uppercase">RESULTS FOR "{searchQuery}"</h2>
        <div className="mx-auto max-w-5xl">
          <CarouselSearchResults
            products={products}
            aiModeEnabled={aiModeEnabled}
            onCurrentProductChange={onCurrentProductChange}
          />
        </div>
      </div>
    </div>
  );
};

export default ShoppingResults;
