import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import {
  Bot,
  MessageCircle,
  Music,
  RotateCw,
  Search,
  Target,
  RotateCcw,
  Minus,
  Plus,
  X,
  Maximize,
  Minimize,
  Mic,
  Footprints,
  <PERSON>rkles,
  RotateCcw as FlipIcon,
  Zap
} from 'lucide-react';
import { RobotState } from '@/hooks/use-robot-animation';

interface RobotControlsProps {
  robotState: RobotState;
  robotScale: number;
  onStateChange: (state: RobotState) => void;
  onScaleChange: (scale: number) => void;
  onCenter: () => void;
  onRoll: () => void;
  onClose?: () => void;
  onFullScreenToggle?: () => void;
  isFullScreenMode?: boolean;
  className?: string;
}

const RobotControls: React.FC<RobotControlsProps> = ({
  robotState,
  robotScale,
  onStateChange,
  onScaleChange,
  onCenter,
  onRoll,
  onClose,
  onFullScreenToggle,
  isFullScreenMode = false,
  className = '',
}) => {
  const scalePercentage = Math.round(robotScale * 100);

  const handleScaleUp = () => {
    onScaleChange(Math.min(1.5, robotScale + 0.1));
  };

  const handleScaleDown = () => {
    onScaleChange(Math.max(0.2, robotScale - 0.1));
  };

  const handleResetScale = () => {
    onScaleChange(0.5);
  };

  return (
    <div className={`fixed left-2 top-1/2 transform -translate-y-1/2 z-50 ${className}`}>
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-md p-2 shadow-lg max-w-[140px]">
        <div className="flex flex-col space-y-1">
          {/* Header with title and close button */}
          <div className="flex items-center justify-between mb-1">
            <div className="text-[10px] font-medium text-gray-600 dark:text-gray-400">
              Controls
            </div>
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-4 w-4 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Close robot controls"
              >
                <X className="h-2 w-2" />
              </Button>
            )}
          </div>

          <Button
            variant={robotState === 'idle' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('idle')}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <Bot className="h-2 w-2 mr-1" />
            Idle
          </Button>

          <Button
            variant={robotState === 'talk' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('talk')}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <MessageCircle className="h-2 w-2 mr-1" />
            Talk
          </Button>

          <Button
            variant={robotState === 'dance' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('dance')}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <Music className="h-2 w-2 mr-1" />
            Dance
          </Button>

          <Button
            variant={robotState === 'search' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('search')}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <Search className="h-2 w-2 mr-1" />
            Search
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onRoll}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <RotateCw className="h-2 w-2 mr-1" />
            Roll
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCenter}
            className="w-full justify-start h-6 text-xs px-2"
          >
            <Target className="h-2 w-2 mr-1" />
            Center
          </Button>

          {onFullScreenToggle && (
            <Button
              variant={isFullScreenMode ? 'default' : 'outline'}
              size="sm"
              onClick={onFullScreenToggle}
              className="w-full justify-start h-6 text-xs px-2"
              title={isFullScreenMode ? 'Exit full screen mode' : 'Enter full screen mode'}
            >
              {isFullScreenMode ? (
                <Minimize className="h-2 w-2 mr-1" />
              ) : (
                <Maximize className="h-2 w-2 mr-1" />
              )}
              {isFullScreenMode ? 'Compact' : 'Full Screen'}
            </Button>
          )}

          {/* Scale Controls */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-1 mt-1">
            <div className="text-[10px] font-medium text-gray-600 dark:text-gray-400 mb-1">
              Size: {scalePercentage}%
            </div>

            <div className="flex items-center space-x-1 mb-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handleScaleDown}
                className="p-0 h-5 w-5"
              >
                <Minus className="h-2 w-2" />
              </Button>

              <div className="flex-1">
                <Slider
                  value={[robotScale]}
                  onValueChange={(value) => onScaleChange(value[0])}
                  min={0.2}
                  max={1.5}
                  step={0.1}
                  className="w-full h-3"
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleScaleUp}
                className="p-0 h-5 w-5"
              >
                <Plus className="h-2 w-2" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleResetScale}
              className="w-full text-[10px] h-5 px-1"
            >
              <RotateCcw className="h-2 w-2 mr-1" />
              Reset
            </Button>
          </div>

          {/* Current State Indicator */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-1 mt-1">
            <div className="text-[10px] text-gray-500 dark:text-gray-400 text-center">
              Status: <span className="font-medium capitalize">{robotState}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RobotControls;
