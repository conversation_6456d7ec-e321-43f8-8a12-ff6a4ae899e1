version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: dev.Dockerfile
    volumes:
      - ..:/app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SESSION_SECRET=${SESSION_SECRET}
    command: sleep infinity
    networks:
      - daswos-network
    ports:
      - "5000:5000"
    depends_on:
      db:
        condition: service_healthy