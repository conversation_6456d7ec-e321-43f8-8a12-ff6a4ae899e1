import React, { useState, useEffect, useCallback } from 'react';
import { Product } from '../../../shared/schema';

interface DaswosHologramPopupProps {
  isVisible: boolean;
  selectedProduct: Product | null;
  popupProducts: Product[];
  currentProductIndex: number;
  robotPosition: { x: number; y: number };
  searchQuery?: string;
  onClose: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onBuyProduct: (product: Product) => void;
  onAddToList: (product: Product) => void;
}

const DaswosHologramPopup: React.FC<DaswosHologramPopupProps> = ({
  isVisible,
  selectedProduct,
  popupProducts,
  currentProductIndex,
  robotPosition,
  searchQuery,
  onClose,
  onPrevious,
  onNext,
  onBuyProduct,
  onAddToList,
}) => {
  const [animationState, setAnimationState] = useState<'front' | 'turning' | 'hologram'>('front');
  const [hologramOpacity, setHologramOpacity] = useState(0);
  const [rotationDegree, setRotationDegree] = useState(0);
  const [showRobotAnimation, setShowRobotAnimation] = useState(false);

  // Start the hologram animation when popup becomes visible
  useEffect(() => {
    if (isVisible && selectedProduct) {
      setShowRobotAnimation(true);
      startHologramAnimation();
    } else {
      resetAnimation();
    }
  }, [isVisible, selectedProduct]);

  const startHologramAnimation = useCallback(() => {
    // Start turning animation
    setAnimationState('turning');
    
    // Animate rotation over time
    let degree = 0;
    const rotationInterval = setInterval(() => {
      degree += 3; // Slightly faster rotation
      setRotationDegree(degree);
      
      if (degree >= 90) {
        clearInterval(rotationInterval);
        setAnimationState('hologram');
        
        // Start hologram fade-in animation
        let opacity = 0;
        const hologramInterval = setInterval(() => {
          opacity += 0.08; // Faster fade-in
          setHologramOpacity(opacity);
          
          if (opacity >= 1) {
            clearInterval(hologramInterval);
          }
        }, 40);
      }
    }, 16); // Smooth 60fps animation
  }, []);

  const resetAnimation = useCallback(() => {
    setAnimationState('front');
    setHologramOpacity(0);
    setRotationDegree(0);
    setShowRobotAnimation(false);
  }, []);

  if (!isVisible || !selectedProduct) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-[90] bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 pointer-events-auto">
      {/* Robot with Hologram Beams - Positioned on Left Side */}
      {showRobotAnimation && (
        <div
          className="absolute"
          style={{
            left: '50px', // Fixed position on left side
            bottom: '20px', // Position at bottom left
            width: '200px',
            height: '250px',
          }}
        >
          {/* Robot with built-in hologram beams */}
          <img
            src="/assets/robot/daswos_hologram_beams.png"
            alt="Daswos Robot with Hologram Beams"
            className="w-full h-full object-contain"
            style={{
              filter: 'drop-shadow(0 0 20px rgba(0, 195, 255, 0.5))',
              opacity: hologramOpacity > 0.5 ? 1 : 0.8
            }}
          />

          {/* Additional beam effects extending toward the hologram */}
          <div
            className="absolute bg-gradient-to-r from-cyan-400 via-cyan-300 to-transparent opacity-60"
            style={{
              left: '180px', // From robot's eye area
              top: '120px',
              width: '400px', // Extend toward center
              height: '4px',
              transform: 'rotate(-5deg)',
              boxShadow: '0 0 15px #00c3ff',
              animation: 'hologramBeam 2s infinite',
              opacity: hologramOpacity
            }}
          />
          <div
            className="absolute bg-gradient-to-r from-cyan-400 via-cyan-300 to-transparent opacity-60"
            style={{
              left: '180px',
              top: '140px',
              width: '400px',
              height: '4px',
              transform: 'rotate(5deg)',
              boxShadow: '0 0 15px #00c3ff',
              animation: 'hologramBeam 2.2s infinite',
              opacity: hologramOpacity
            }}
          />
        </div>
      )}

      {/* Enhanced Hologram Display - Only show when animation reaches hologram state */}
      {animationState === 'hologram' && (
        <div
          className="absolute bg-gradient-to-br from-cyan-400/20 via-blue-500/15 to-purple-600/20 backdrop-blur-sm border border-cyan-400/50 rounded-lg shadow-2xl pointer-events-auto"
          style={{
            left: '50%',
            top: '50%',
            width: '400px',
            height: '500px',
            transform: 'translateX(-50%) translateY(-50%)',
            background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.95) 100%)',
            boxShadow: '0 0 60px rgba(6, 182, 212, 0.4), inset 0 0 60px rgba(6, 182, 212, 0.1)',
            opacity: hologramOpacity,
            animation: 'hologramFlicker 3s infinite, hologramFloat 4s ease-in-out infinite'
          }}
        >
          {/* Hologram scanning lines overlay */}
          <div 
            className="absolute inset-0 opacity-30 pointer-events-none rounded-lg"
            style={{
              background: `repeating-linear-gradient(
                0deg,
                rgba(0, 195, 255, 0.1) 0px,
                rgba(0, 195, 255, 0.1) 1px,
                transparent 1px,
                transparent 6px
              )`,
              animation: 'scanLines 2s infinite linear'
            }}
          />

          {/* Close Button */}
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('❌ Closing Daswos hologram');
              onClose();
            }}
            className="absolute top-3 right-3 z-20 w-8 h-8 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors cursor-pointer text-lg border border-cyan-400/30"
            title="Close Hologram"
          >
            ✕
          </button>

          {/* Hologram Header */}
          <div className="p-6 border-b border-cyan-400/30">
            <h2 className="text-xl font-bold text-cyan-100 text-center tracking-wider">
              🤖 DASWOS PRODUCT SCAN
            </h2>
            <p className="text-sm text-cyan-300/80 text-center mt-2">
              {searchQuery ? `"${searchQuery}"` : 'PRODUCT ANALYSIS'}
            </p>
          </div>

          {/* Hologram Product Display */}
          <div className="p-6">
            <div className="relative">
              {/* Navigation Arrows */}
              {popupProducts.length > 1 && (
                <>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onPrevious();
                    }}
                    className="absolute left-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-all cursor-pointer text-xl font-bold border border-cyan-400/30"
                    title="Previous product"
                  >
                    ‹
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onNext();
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 bg-cyan-400/20 hover:bg-cyan-400/40 rounded-full flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-all cursor-pointer text-xl font-bold border border-cyan-400/30"
                    title="Next product"
                  >
                    ›
                  </button>
                </>
              )}

              {/* Product Display */}
              <div className="text-center">
                {/* Product Image with Enhanced Hologram Effect */}
                <div className="relative mb-4">
                  <div className="bg-cyan-400/10 border-2 border-cyan-400/40 rounded-xl p-4 h-40 flex items-center justify-center relative overflow-hidden">
                    {/* Enhanced scanning effect */}
                    <div 
                      className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/30 to-transparent h-4"
                      style={{ animation: 'scanLine 2.5s infinite linear' }} 
                    />

                    {/* Product Image - Show actual colors without filters */}
                    <img
                      src={selectedProduct.imageUrl || selectedProduct.image_url || selectedProduct.image || '/placeholder-product.jpg'}
                      alt={selectedProduct.title}
                      className="max-w-full max-h-full object-contain"
                      style={{ 
                        filter: 'none', // Remove any filters to show actual colors
                        opacity: 0.95 
                      }}
                    />

                    {/* Hologram grid overlay - more subtle */}
                    <div 
                      className="absolute inset-0 opacity-15 pointer-events-none"
                      style={{
                        backgroundImage: 'linear-gradient(rgba(6, 182, 212, 0.4) 1px, transparent 1px), linear-gradient(90deg, rgba(6, 182, 212, 0.4) 1px, transparent 1px)',
                        backgroundSize: '25px 25px'
                      }} 
                    />
                  </div>
                </div>

                {/* Product Information */}
                <div className="mb-4">
                  <h3 className="text-lg font-bold text-cyan-100 mb-2 tracking-wide">
                    {selectedProduct.title}
                  </h3>
                  <p className="text-2xl font-bold text-cyan-300 mb-2">
                    ${(selectedProduct.price / 100).toFixed(2)}
                  </p>
                  <p className="text-sm text-cyan-400/80">
                    Stock: {selectedProduct.quantity} • Seller: {selectedProduct.sellerName}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black font-bold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 text-base"
                    disabled={!(selectedProduct.quantity > 0 && selectedProduct.status === 'active')}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onBuyProduct(selectedProduct);
                    }}
                    style={{
                      boxShadow: '0 0 20px rgba(255, 193, 7, 0.5)'
                    }}
                  >
                    🪙 BUY WITH DASWOS COINS
                  </button>
                  <button
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 text-base"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onAddToList(selectedProduct);
                    }}
                    style={{
                      boxShadow: '0 0 20px rgba(147, 51, 234, 0.5)'
                    }}
                  >
                    📋 ADD TO LIST
                  </button>
                </div>
              </div>

              {/* Product Counter */}
              {popupProducts.length > 1 && (
                <div className="text-center mt-6 text-cyan-400/80 text-sm">
                  📊 Product {currentProductIndex + 1} of {popupProducts.length}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced CSS animations */}
      <style jsx>{`
        @keyframes hologramBeam {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.1); }
        }
        
        @keyframes hologramFlicker {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.95; }
        }
        
        @keyframes hologramFloat {
          0%, 100% { transform: translateX(-50%) translateY(-50%) translateZ(0); }
          50% { transform: translateX(-50%) translateY(-52%) translateZ(0); }
        }
        
        @keyframes scanLine {
          0% { top: 0%; }
          100% { top: 100%; }
        }
        
        @keyframes scanLines {
          0% { transform: translateY(0); }
          100% { transform: translateY(6px); }
        }
      `}</style>
    </div>
  );
};

export default DaswosHologramPopup;
