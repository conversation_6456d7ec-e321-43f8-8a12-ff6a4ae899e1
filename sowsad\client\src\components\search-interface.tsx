import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Search, Sun, Moon, Image, X, ShoppingCart } from 'lucide-react';
import { useTheme } from '@/providers/theme-provider';
import DasWosLogo from '@/components/daswos-logo';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import AnimatedTrustText from '@/components/animated-trust-text';
import StatusLabel from '@/components/status-label';
import ShoppingResults from '@/components/shopping-results';
import InformationResults from '@/components/information-results';
import WhisperVoiceControl from '@/components/whisper-voice-control';
import { Product } from '@shared/schema';

interface SearchInterfaceProps {
  onSearch: (query: string) => void;
  aiModeEnabled?: boolean;
  onToggleAi?: (enabled: boolean) => void;
  activeSphere?: 'safesphere' | 'opensphere';
  onSphereChange?: (sphere: 'safesphere' | 'opensphere') => void;
  superSafeActive?: boolean;
  onToggleSuperSafe?: (active: boolean) => void;
  className?: string;
  showResults?: boolean;
  selectedResultType?: 'shopping' | 'information' | null;
  searchQuery?: string;
  onBuyCurrentProduct?: () => void;
  hasShoppingResults?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
  onRobotActivate?: () => void; // New prop for robot activation
  isRobotActive?: boolean; // New prop for robot state
}

const SearchInterface: React.FC<SearchInterfaceProps> = ({
  onSearch,
  aiModeEnabled = false,
  onToggleAi,
  activeSphere = 'safesphere',
  onSphereChange,
  superSafeActive = false,
  onToggleSuperSafe,
  className = '',
  showResults = false,
  selectedResultType = null,
  searchQuery = '',
  onBuyCurrentProduct,
  hasShoppingResults = false,
  onCurrentProductChange,
  onRobotActivate,
  isRobotActive = false
}) => {
  // Reference to the container
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [currentQuery, setCurrentQuery] = useState(searchQuery);
  const [isQueryBeingEdited, setIsQueryBeingEdited] = useState(false);
  const [lastSubmittedQuery, setLastSubmittedQuery] = useState(searchQuery);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [aiResponseText, setAiResponseText] = useState<string>('');
  const [voiceStatus, setVoiceStatus] = useState<string>('');

  // Update currentQuery when searchQuery prop changes
  useEffect(() => {
    if (searchQuery && searchQuery !== currentQuery) {
      setCurrentQuery(searchQuery);
      setLastSubmittedQuery(searchQuery);
      setIsQueryBeingEdited(false);
    }
  }, [searchQuery]);

  // Detect when user is editing the query
  const handleQueryChange = (value: string) => {
    setCurrentQuery(value);

    // If AI mode is enabled and we have shopping results, check if query has changed
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const isEditing = value.trim() !== lastSubmittedQuery.trim();
      setIsQueryBeingEdited(isEditing);

      // If user is editing, clear the current product to hide buy button
      if (isEditing && onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input focus - immediately switch to editing mode
  const handleInputFocus = () => {
    setIsInputFocused(true);

    // If AI mode is enabled and we have shopping results, switch to editing mode
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      setIsQueryBeingEdited(true);

      // Clear the current product to hide buy button
      if (onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input blur - check if we should exit editing mode
  const handleInputBlur = () => {
    setIsInputFocused(false);

    // Only exit editing mode if the query matches the last submitted query
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const queryMatches = currentQuery.trim() === lastSubmittedQuery.trim();
      if (queryMatches) {
        setIsQueryBeingEdited(false);
        // Note: We don't restore the current product here as it will be set by the search results
      }
    }
  };

  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [, navigate] = useLocation();

  // Voice command handlers
  const handleVoiceSearchCommand = (query: string) => {
    // Clear voice status first so the search query shows in the input
    setVoiceStatus('');
    setCurrentQuery(query);
    onSearch(query);
    setLastSubmittedQuery(query);
    setIsQueryBeingEdited(false);

    toast({
      title: 'Voice Search',
      description: `"${query}"`,
    });
  };

  const handleVoiceAutoShopCommand = () => {
    // Trigger AutoShop settings dialog by setting the query to a known AutoShop trigger
    const autoShopQuery = "let's autoshop";
    setCurrentQuery(autoShopQuery);
    onSearch(autoShopQuery);
    setLastSubmittedQuery(autoShopQuery);
    setIsQueryBeingEdited(false);

    toast({
      title: 'Voice Command Executed',
      description: 'Opening AutoShop settings...',
    });
  };

  // Listen for AI voice events from WhisperVoiceControl
  useEffect(() => {
    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query) {
        console.log('🔍 AI Search command received:', query);
        handleVoiceSearchCommand(query);
      }
    };

    const handleAIAutoshop = () => {
      console.log('🛒 AI AutoShop command received');
      handleVoiceAutoShopCommand();
    };

    // Listen for voice status updates
    const handleVoiceStatus = (event: CustomEvent) => {
      const { status, message } = event.detail;
      console.log('🎤 Voice status:', { status, message });

      // Show voice status in search bar
      if (status === 'listening') {
        setVoiceStatus('🎤 Listening... Speak now');
        setCurrentQuery('');
        console.log('🌈 Setting listening status for border animation');
      } else if (status === 'processing') {
        setVoiceStatus('🤖 Processing your request...');
      } else if (status === 'speaking') {
        setVoiceStatus('🗣️ DasWos AI is responding...');
      } else if (status === 'idle') {
        setVoiceStatus('');
        console.log('🌈 Clearing voice status - border animation should stop');
      } else if (message) {
        setVoiceStatus(message);
      }
    };

    // Listen for voice command results from WhisperVoiceControl
    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse, audio } = event.detail;
      console.log('🎤 Voice command result:', { userQuery, aiResponse });

      if (userQuery) {
        // Show what the user said in the search bar first
        setCurrentQuery(userQuery);
        setVoiceStatus('');

        // Show AI response in search bar
        if (aiResponse) {
          // Store AI response for display - ensure it's always a string
          const responseText = typeof aiResponse === 'string' ? aiResponse : (aiResponse.response || 'Processing...');

          // Show AI typing effect
          setVoiceStatus('🤖 DasWos AI: ' + responseText);

          // Handle different AI intents
          if (aiResponse.intent === 'search' && aiResponse.parameters?.query) {
            console.log('🔍 Executing search for:', aiResponse.parameters.query);
            // Update search query to what AI wants to search for
            setTimeout(() => {
              setCurrentQuery(aiResponse.parameters.query);
              setLastSubmittedQuery(aiResponse.parameters.query);
              setVoiceStatus('');
              onSearch(aiResponse.parameters.query);
            }, 2000);
          } else if (aiResponse.intent === 'navigation' && aiResponse.parameters?.route) {
            console.log('🧭 Navigating to:', aiResponse.parameters.route);
            setTimeout(() => {
              setVoiceStatus('');
              navigate(aiResponse.parameters.route);
            }, 2000);
          } else if (aiResponse.intent === 'autoshop') {
            console.log('🛒 Triggering AutoShop');
            setTimeout(() => {
              setVoiceStatus('');
              // Dispatch AutoShop event
              const autoshopEvent = new CustomEvent('aiAutoshop');
              window.dispatchEvent(autoshopEvent);
            }, 2000);
          } else {
            // For conversation responses, show the response in the search bar
            console.log('💬 Conversation response:', responseText);
            // Clear after 5 seconds
            setTimeout(() => {
              setVoiceStatus('');
            }, 5000);
          }
        }
      }
    };

    // Listen for direct voice search events
    const handleVoiceSearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query) {
        console.log('🎤 Direct voice search received:', query);
        // Clear voice status immediately when voice search is received
        setVoiceStatus('');
        handleVoiceSearchCommand(query);
      }
    };

    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    window.addEventListener('voiceStatus', handleVoiceStatus as EventListener);
    window.addEventListener('voiceSearch', handleVoiceSearch as EventListener);

    return () => {
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
      window.removeEventListener('voiceStatus', handleVoiceStatus as EventListener);
      window.removeEventListener('voiceSearch', handleVoiceSearch as EventListener);
    };
  }, [navigate, onSearch, toast]);

  // Function to reset search and go to home page
  const resetSearchAndGoHome = () => {
    // Clear the search input
    setCurrentQuery('');
    // Dispatch a custom event to reset the search state in the parent component
    const resetEvent = new CustomEvent('resetSearchInterface', {
      detail: { reset: true }
    });
    window.dispatchEvent(resetEvent);
  };

  // No drag functionality - interface is fixed in position

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // If AI mode is enabled, has shopping results, not being edited, not focused, and we have a buy handler, trigger buy
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused && onBuyCurrentProduct) {
      onBuyCurrentProduct();
      return;
    }

    // Otherwise, perform normal search
    if (currentQuery.trim()) {
      onSearch(currentQuery.trim());
      setLastSubmittedQuery(currentQuery.trim());
      setIsQueryBeingEdited(false);
      // Blur the input to exit focus mode
      if (searchInputRef.current) {
        searchInputRef.current.blur();
      }
      // Don't clear the input - keep it for display in results
      // setCurrentQuery(''); // Removed to preserve search query
    }
  };



  return (
    <div
      ref={containerRef}
      className={`search-interface ${className}`}
      style={{
        position: 'relative',
        width: '932px', // Fixed width for the search bar
        margin: '0 auto',
        backgroundColor: 'transparent',
        padding: showResults ? '10px 0 0 0' : '15px 0 0 0',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        marginTop: showResults ? '-50px' : '0', // Move up when showing results
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: showResults ? 'auto' : '30vh', // Center vertically when no results
        transform: 'translateX(-40px)' // Shift left to balance the spacing
      }}
    >
      {/* No drag handle needed */}

      {/* Logo and buttons - only shown when no results */}
      {!showResults && (
        <div className="flex flex-col items-center justify-center mb-3">
          <div className="relative inline-block">
            <div className="py-1 flex justify-center">
              <div
                onClick={resetSearchAndGoHome}
                className="cursor-pointer hover:opacity-80 transition-opacity"
                title="Return to home page"
              >
                <DasWosLogo height={40} width="auto" />
              </div>
            </div>

            {/* Animated Trust Heading */}
            <div className="mt-1 mb-2 w-full text-center text-xs">
              <AnimatedTrustText
                sentences={[
                  "Helping you find what you need with confidence."
                ]}
                duration={5000}
              />
            </div>

            {/* Buttons container */}
            <div className="absolute right-[-60px] top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
              {/* Theme Toggle Button */}
              <button
                onClick={toggleTheme}
                className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
                aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-gray-400" />
                ) : (
                  <Moon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search form */}
      <form onSubmit={handleSubmit} className={`flex flex-col space-y-2 px-4 pb-2 ${showResults ? 'mt-0' : ''} w-full`}>
        {showResults && (
          <div className="flex items-center mb-1.5">
            <div
              onClick={resetSearchAndGoHome}
              className="cursor-pointer hover:opacity-80 transition-opacity"
              title="Return to home page"
            >
              <DasWosLogo height={30} width="auto" className="mr-3" />
            </div>
            <div className="flex-1"></div>

            {/* Theme Toggle Button when results are shown */}
            <button
              onClick={toggleTheme}
              className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {theme === 'dark' ? (
                <Sun className="h-5 w-5 text-gray-400" />
              ) : (
                <Moon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        )}
        <div className="relative flex items-center gap-2 w-full max-w-[932px] mx-auto">
          {/* Voice Control Button - positioned to the left of search bar */}
          {aiModeEnabled && (
            <div className="flex-shrink-0">
              <WhisperVoiceControl
                className=""
                enableTextToSpeech={true}
                isAiModeEnabled={aiModeEnabled}
                onRobotActivate={onRobotActivate}
                isRobotActive={isRobotActive}
              />
            </div>
          )}

          {/* Main search container */}
          <div className="relative flex w-full">
            {/* Search bar container */}
            <div className={`relative flex w-full bg-white dark:bg-gray-800 border rounded-md shadow-sm overflow-hidden z-10 ${
              aiModeEnabled
                ? 'border-blue-500'
                : 'border-gray-300 dark:border-gray-600'
            }`}>



            <input
              type="text"
              placeholder={
                voiceStatus
                  ? voiceStatus
                  : aiModeEnabled && aiResponseText
                  ? `DasWos AI: ${aiResponseText}`
                  : "What are you looking for?"
              }
              value={voiceStatus ? '' : currentQuery}
              onChange={(e) => handleQueryChange(e.target.value)}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              maxLength={100}
              className={`w-full px-4 py-2 text-sm bg-transparent focus:outline-none border-0 rounded-l-md h-[38px] ${
                voiceStatus
                  ? 'text-blue-600 dark:text-blue-400 placeholder-blue-600 dark:placeholder-blue-400'
                  : 'text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400'
              }`}
              ref={searchInputRef}
              readOnly={!!voiceStatus}
            />
            <button
              type="submit"
              className={`px-4 search-button rounded-r-md border-l ${
                aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused
                  ? 'bg-green-600 hover:bg-green-700 text-white border-gray-300 dark:border-gray-600'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600'
              } transition-colors duration-200 h-[38px] flex items-center justify-center`}
              aria-label={aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused ? "Execute Purchase" : "Search"}
            >
              {/* Button content */}
              <div className="flex items-center gap-1">
                {aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused ? (
                  <>
                    <ShoppingCart className="h-4 w-4" />
                    <span className="text-xs font-semibold">BUY</span>
                  </>
                ) : (
                  <Search className={`h-4 w-4 ${aiModeEnabled ? 'text-blue-500' : 'text-gray-600 dark:text-gray-300'}`} />
                )}
              </div>
            </button>


            </div>
          </div>
        </div>

        {/* Feature buttons */}
        <div className="flex justify-center mt-1.5 space-x-2 w-full max-w-[932px] mx-auto">
          {/* SafeSphere button */}
          <div
            className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${activeSphere === 'safesphere' ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => onSphereChange && onSphereChange(activeSphere === 'safesphere' ? 'opensphere' : 'safesphere')}
          >
            {/* Square checkbox */}
            <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {activeSphere === 'safesphere' && (
                <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Shield icon */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text */}
            <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SafeSphere</span>

            {/* Status label - only shown when active */}
            {activeSphere === 'safesphere' && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[55px] text-right pr-1">Protected</span>
            )}
          </div>

          {/* DasWos AI button with dropdown */}
          <div className="relative">
            {/* Main button */}
            <div
              className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${aiModeEnabled ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
              onClick={() => onToggleAi && onToggleAi(!aiModeEnabled)}
            >
              {/* Square checkbox */}
              <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
                {aiModeEnabled && (
                  <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  </svg>
                )}
              </div>

              {/* TV/Computer icon */}
              <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></rect>
                <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
                <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
              </svg>

              {/* Text */}
              <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">Daswos AI</span>

              {/* Status label - only shown when active */}
              {aiModeEnabled && (
                <span className="ml-auto text-blue-500 text-[8px] font-medium w-[50px] text-right pr-1">Enabled</span>
              )}
            </div>
          </div>

          {/* SuperSafe button */}
          <div
            className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${superSafeActive ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => onToggleSuperSafe && onToggleSuperSafe(!superSafeActive)}
          >
            {/* Square checkbox */}
            <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {superSafeActive && (
                <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Circle check icon */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text */}
            <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SuperSafe</span>

            {/* Status label - only shown when active */}
            {superSafeActive && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[35px] text-right pr-1">Active</span>
            )}
          </div>
        </div>

      </form>

      {/* Search Results */}
      {showResults && selectedResultType && (
        <div className="mt-4 w-full max-w-[932px] mx-auto">
          {selectedResultType === 'shopping' ? (
            <ShoppingResults
              searchQuery={searchQuery}
              sphere={activeSphere}
              className="mt-2"
              aiModeEnabled={aiModeEnabled}
              onCurrentProductChange={onCurrentProductChange}
            />
          ) : (
            <InformationResults
              searchQuery={searchQuery}
              sphere={activeSphere}
              className="mt-2"
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SearchInterface;
