import { Router } from 'express';
import { RobotPositioningService } from '../services/robot-positioning';
import { requireAuth } from '../auth';
import { log } from '../vite';

const router = Router();

/**
 * GET /api/robot-positioning/next-position
 * Get the next position for the current user
 */
router.get('/next-position', requireAuth, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get screen dimensions from query params (with defaults)
    const screenWidth = parseInt(req.query.screenWidth as string) || 1920;
    const screenHeight = parseInt(req.query.screenHeight as string) || 1080;

    log(`🎯 Getting next position for user ${userId} (screen: ${screenWidth}x${screenHeight})`);

    // Ensure user has an algorithm assignment
    await RobotPositioningService.ensureUserHasAlgorithm(userId);

    // Get next position
    const position = await RobotPositioningService.getNextPositionForUser(
      userId, 
      screenWidth, 
      screenHeight
    );

    if (!position) {
      return res.status(404).json({ error: 'No position available' });
    }

    res.json({
      position,
      screenWidth,
      screenHeight,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    log(`❌ Error getting next position: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to get next position' });
  }
});

/**
 * GET /api/robot-positioning/user-algorithm
 * Get current algorithm assignment for the user
 */
router.get('/user-algorithm', requireAuth, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const assignment = await RobotPositioningService.getUserAlgorithmAssignment(userId);
    
    if (!assignment) {
      return res.status(404).json({ error: 'No algorithm assignment found' });
    }

    res.json({
      assignment: {
        id: assignment.id,
        algorithmId: assignment.algorithmId,
        currentPositionIndex: assignment.currentPositionIndex,
        assignedAt: assignment.assignedAt,
        expiresAt: assignment.expiresAt,
        isActive: assignment.isActive
      }
    });

  } catch (error) {
    log(`❌ Error getting user algorithm: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to get user algorithm' });
  }
});

/**
 * GET /api/robot-positioning/stats
 * Get algorithm statistics (admin only)
 */
router.get('/stats', requireAuth, async (req, res) => {
  try {
    // Check if user is admin (optional - you can remove this check if you want all users to see stats)
    if (!req.user?.isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const stats = await RobotPositioningService.getAlgorithmStats();
    res.json(stats);

  } catch (error) {
    log(`❌ Error getting algorithm stats: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to get algorithm stats' });
  }
});

/**
 * POST /api/robot-positioning/rotate
 * Manually trigger algorithm rotation (admin only)
 */
router.post('/rotate', requireAuth, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    await RobotPositioningService.rotateAlgorithms();
    
    res.json({ 
      success: true, 
      message: 'Algorithm rotation completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    log(`❌ Error rotating algorithms: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to rotate algorithms' });
  }
});

/**
 * POST /api/robot-positioning/create-algorithm
 * Create a new positioning algorithm (admin only)
 */
router.post('/create-algorithm', requireAuth, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { name, screenWidth, screenHeight, totalPositions, minDistance, safeMargin } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Algorithm name is required' });
    }

    const algorithm = await RobotPositioningService.createAlgorithm(name, {
      screenWidth,
      screenHeight,
      totalPositions,
      minDistance,
      safeMargin
    });

    res.json({
      success: true,
      algorithm: {
        id: algorithm.id,
        name: algorithm.name,
        totalPositions: algorithm.totalPositions,
        screenWidth: algorithm.screenWidth,
        screenHeight: algorithm.screenHeight,
        createdAt: algorithm.createdAt
      }
    });

  } catch (error) {
    log(`❌ Error creating algorithm: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to create algorithm' });
  }
});

/**
 * GET /api/robot-positioning/algorithms
 * Get all active algorithms (admin only)
 */
router.get('/algorithms', requireAuth, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const algorithms = await RobotPositioningService.getActiveAlgorithms();
    
    res.json({
      algorithms: algorithms.map(alg => ({
        id: alg.id,
        name: alg.name,
        totalPositions: alg.totalPositions,
        screenWidth: alg.screenWidth,
        screenHeight: alg.screenHeight,
        isActive: alg.isActive,
        createdAt: alg.createdAt
      }))
    });

  } catch (error) {
    log(`❌ Error getting algorithms: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to get algorithms' });
  }
});

/**
 * POST /api/robot-positioning/ensure-assignment
 * Ensure current user has an algorithm assignment
 */
router.post('/ensure-assignment', requireAuth, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const assignment = await RobotPositioningService.ensureUserHasAlgorithm(userId);
    
    if (!assignment) {
      return res.status(500).json({ error: 'Failed to assign algorithm' });
    }

    res.json({
      success: true,
      assignment: {
        id: assignment.id,
        algorithmId: assignment.algorithmId,
        currentPositionIndex: assignment.currentPositionIndex,
        assignedAt: assignment.assignedAt,
        expiresAt: assignment.expiresAt,
        isActive: assignment.isActive
      }
    });

  } catch (error) {
    log(`❌ Error ensuring assignment: ${error}`, 'error');
    res.status(500).json({ error: 'Failed to ensure assignment' });
  }
});

export default router;
