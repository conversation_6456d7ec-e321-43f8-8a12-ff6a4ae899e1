/* Navigation Bar Styles */

.nav-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  padding: 0;
  transition: all 0.2s ease;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

/* Home logo button styling */
.home-logo-button {
  background-color: black;
  color: white;
  border-radius: 8px;
  overflow: visible;
}

.home-logo-button:hover {
  background-color: #333;
}

.home-logo-button .dasbar-logo {
  height: 24px;
  width: 24px;
}

/* Back button styling */
.back-button {
  background-color: #E0E0E0;
  color: #000000;
  border: 1px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background-color: #d0d0d0;
}

/* Forward button styling */
.forward-button {
  background-color: #E0E0E0;
  color: #000000;
  border: 1px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
}

.forward-button:hover {
  background-color: #d0d0d0;
}

/* Dasbar item styling */
.dasbar-item {
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
}

.dasbar-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dasbar-item::after {
  content: attr(title);
  position: absolute;
  left: 50px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
}

.dasbar-item:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Dark mode styles */
.dark .back-button,
.dark .forward-button {
  background-color: #333333;
  color: #ffffff;
  border-color: #4b5563;
}

.dark .back-button:hover,
.dark .forward-button:hover {
  background-color: #4b5563;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .nav-button {
    height: 36px;
    width: 36px;
  }

  .dasbar-item {
    height: 36px !important;
    width: 36px !important;
  }
}
