import React from 'react';
import { Mic } from 'lucide-react';
import { Button } from './ui/button';
import WhisperVoiceControl from './whisper-voice-control';

interface GlobalVoiceSearchButtonProps {
  className?: string;
}

const GlobalVoiceSearchButton: React.FC<GlobalVoiceSearchButtonProps> = ({
  className = ''
}) => {
  return (
    <div className={`fixed bottom-[75px] right-[20px] z-[1002] ${className}`}>
      <div className="flex flex-col items-end space-y-2">
        <WhisperVoiceControl
          className="rounded-[8px] bg-white dark:bg-gray-800 shadow-md border border-gray-300 dark:border-gray-600"
          enableTextToSpeech={true}
          isAiModeEnabled={true}
        />
      </div>
    </div>
  );
};

export default GlobalVoiceSearchButton;
