import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Bo<PERSON> } from 'lucide-react';
import { Button } from './ui/button';
import WhisperVoiceControl from './whisper-voice-control';

interface GlobalVoiceSearchButtonProps {
  className?: string;
}

const GlobalVoiceSearchButton: React.FC<GlobalVoiceSearchButtonProps> = ({
  className = ''
}) => {
  const [isRobotActive, setIsRobotActive] = useState(false);

  const handleSpeakToDaswos = () => {
    // First activate the robot
    const activatedEvent = new CustomEvent('activateRobot');
    window.dispatchEvent(activatedEvent);

    // Then trigger voice recording for Daswos AI
    setTimeout(() => {
      const speakEvent = new CustomEvent('startDaswosVoice');
      window.dispatchEvent(speakEvent);
    }, 500); // Small delay to let robot activate first
  };

  // Listen for robot activation/deactivation events
  useEffect(() => {
    const handleRobotActivated = () => setIsRobotActive(true);
    const handleRobotDeactivated = () => setIsRobotActive(false);

    window.addEventListener('robotActivated', handleRobotActivated);
    window.addEventListener('robotDeactivated', handleRobotDeactivated);

    return () => {
      window.removeEventListener('robotActivated', handleRobotActivated);
      window.removeEventListener('robotDeactivated', handleRobotDeactivated);
    };
  }, []);

  return (
    <div className={`fixed bottom-[135px] right-[20px] z-[1002] ${className}`}>
      <div className="flex flex-col items-end space-y-2">
        {/* Speak to Daswos Button - hide when robot is active */}
        {!isRobotActive && (
          <Button
            onClick={handleSpeakToDaswos}
            variant="outline"
            size="icon"
            className="rounded-[8px] bg-white dark:bg-gray-800 shadow-md h-16 w-16 border border-gray-300 dark:border-gray-600 hover:bg-green-50 dark:hover:bg-green-900"
            title="Speak to Daswos"
          >
            <div className="flex flex-col items-center">
              <Mic className="h-5 w-5 text-green-500 mb-1" />
              <span className="text-[8px] text-green-500 font-medium">Speak</span>
            </div>
          </Button>
        )}

        {/* Voice Search Button */}
        <WhisperVoiceControl
          className="rounded-[8px] bg-white dark:bg-gray-800 shadow-md border border-gray-300 dark:border-gray-600"
          enableTextToSpeech={true}
          isAiModeEnabled={true}
        />
      </div>
    </div>
  );
};

export default GlobalVoiceSearchButton;
