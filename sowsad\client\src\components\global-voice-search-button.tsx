import React from 'react';
import { Mi<PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import WhisperVoiceControl from './whisper-voice-control';

interface GlobalVoiceSearchButtonProps {
  className?: string;
}

const GlobalVoiceSearchButton: React.FC<GlobalVoiceSearchButtonProps> = ({
  className = ''
}) => {
  const handleDaswosActivation = () => {
    // Dispatch robot activation event
    const activatedEvent = new CustomEvent('activateRobot');
    window.dispatchEvent(activatedEvent);
  };

  return (
    <div className={`fixed bottom-[135px] right-[20px] z-[1002] ${className}`}>
      <div className="flex flex-col items-end space-y-2">
        {/* Daswos AI Activation Button */}
        <Button
          onClick={handleDaswosActivation}
          variant="outline"
          size="icon"
          className="rounded-[8px] bg-white dark:bg-gray-800 shadow-md h-16 w-16 border border-gray-300 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-blue-900"
          title="Activate Daswos AI"
        >
          <Bot className="h-8 w-8 text-blue-500" />
        </Button>

        {/* Voice Search Button */}
        <WhisperVoiceControl
          className="rounded-[8px] bg-white dark:bg-gray-800 shadow-md border border-gray-300 dark:border-gray-600"
          enableTextToSpeech={true}
          isAiModeEnabled={true}
        />
      </div>
    </div>
  );
};

export default GlobalVoiceSearchButton;
