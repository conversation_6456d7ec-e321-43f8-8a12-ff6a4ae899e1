-- UNIFIED SCHEMA FOR DASWOS-18 AND CURRENT-BROBOT-1
-- This schema combines both apps and includes all important tables
-- SINGLE FILE - COMPLETE SETUP

-- Clean up any existing objects first
DROP VIEW IF EXISTS products_legacy CASCADE;
DROP VIEW IF EXISTS categories_legacy CASCADE;
DROP TABLE IF EXISTS user_product_content CASCADE;
DROP TABLE IF EXISTS seller_verification CASCADE;
DROP TABLE IF EXIS<PERSON> purchases CASCADE;
DROP TABLE IF EXISTS information_content CASCADE;
DROP TABLE IF EXISTS cart_items CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP FUNCTION IF EXISTS search_products(TEXT);
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (enhanced from daswos-18 schema)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_seller BOOLEAN DEFAULT false NOT NULL,
    is_admin BOOLEAN DEFAULT false NOT NULL,
    avatar TEXT,
    has_subscription BOOLEAN DEFAULT false NOT NULL,
    subscription_type TEXT, -- "limited", "unlimited", or legacy types
    subscription_expires_at TIMESTAMP WITH TIME ZONE,

    -- Family account fields
    is_family_owner BOOLEAN DEFAULT false NOT NULL,
    family_owner_id INTEGER,
    parent_account_id INTEGER,
    is_child_account BOOLEAN DEFAULT false NOT NULL,

    -- SuperSafe mode fields
    super_safe_mode BOOLEAN DEFAULT false NOT NULL,
    super_safe_settings JSONB DEFAULT '{"blockGambling": true, "blockAdultContent": true, "blockOpenSphere": false}',
    safe_sphere_active BOOLEAN DEFAULT false NOT NULL,

    -- AI Shopper settings
    ai_shopper_enabled BOOLEAN DEFAULT false NOT NULL,
    ai_shopper_settings JSONB DEFAULT '{}',

    -- Identity verification fields
    identity_verified BOOLEAN DEFAULT false NOT NULL,
    identity_verification_status TEXT DEFAULT 'none' NOT NULL,
    identity_verification_submitted_at TIMESTAMP WITH TIME ZONE,
    identity_verification_approved_at TIMESTAMP WITH TIME ZONE,
    identity_verification_data JSONB DEFAULT '{}',

    -- Trust score and DasWos Coins balance (user-owned)
    trust_score INTEGER DEFAULT 75 NOT NULL, -- Increased default for SafeSphere compatibility
    daswos_coins_balance INTEGER DEFAULT 0 NOT NULL, -- User's DasWos coins balance
    wallet_id TEXT, -- User's connected wallet ID (for balance access)

    -- Business information
    business_name TEXT,
    business_type TEXT DEFAULT 'individual' NOT NULL,
    business_address TEXT,
    contact_phone TEXT,
    tax_id TEXT,
    website TEXT,
    year_established INTEGER,
    business_description TEXT,
    profile_image_url TEXT,
    document_urls TEXT[],

    updated_at TIMESTAMP WITH TIME ZONE
);

-- Categories table (compatible with both apps)
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    parent_id INTEGER,
    level INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Products table (enhanced from both schemas)
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    price INTEGER NOT NULL, -- In cents for consistency with daswos-18
    image_url TEXT NOT NULL,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    seller_verified BOOLEAN NOT NULL DEFAULT false,
    seller_type TEXT NOT NULL DEFAULT 'merchant',
    trust_score INTEGER NOT NULL DEFAULT 75, -- Increased default for SafeSphere compatibility
    identity_verified BOOLEAN NOT NULL DEFAULT true, -- Default true for SafeSphere compatibility
    identity_verification_status TEXT NOT NULL DEFAULT 'approved', -- Default approved for SafeSphere compatibility
    tags TEXT[] NOT NULL DEFAULT '{}',
    shipping TEXT NOT NULL DEFAULT 'standard',
    original_price INTEGER,
    discount INTEGER,
    verified_since TEXT,
    warning TEXT,
    is_bulk_buy BOOLEAN DEFAULT false NOT NULL,
    bulk_minimum_quantity INTEGER,
    bulk_discount_rate INTEGER,
    image_description TEXT,
    category_id INTEGER,
    ai_attributes JSONB DEFAULT '{}',
    search_vector TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    quantity INTEGER NOT NULL DEFAULT 1,
    sold_quantity INTEGER NOT NULL DEFAULT 0,
    in_stock BOOLEAN DEFAULT true, -- For compatibility with current-brobot-1
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- User Sessions table (enhanced from both schemas)
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Cart Items table (enhanced from both schemas)
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    session_id INTEGER, -- For guest users
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    source TEXT NOT NULL DEFAULT 'manual', -- "manual", "ai_shopper", "saved_for_later"
    recommendation_id INTEGER
);

-- Information Content table (from daswos-18)
CREATE TABLE information_content (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT NOT NULL,
    source_url TEXT NOT NULL,
    source_name TEXT NOT NULL,
    source_verified BOOLEAN NOT NULL DEFAULT false,
    source_type TEXT NOT NULL DEFAULT 'website',
    trust_score INTEGER NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] NOT NULL,
    image_url TEXT,
    verified_since TEXT,
    warning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Purchases table (from daswos-18)
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER NOT NULL,
    seller_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price INTEGER NOT NULL, -- Price in cents
    status TEXT NOT NULL DEFAULT 'pending',
    transaction_id INTEGER,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    received_at TIMESTAMP WITH TIME ZONE,
    rating INTEGER,
    review_comment TEXT,
    rated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Seller Verification table (from daswos-18)
CREATE TABLE seller_verification (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL, -- "basic", "priority", "personal"
    status TEXT NOT NULL DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    deposit_amount INTEGER,
    comments TEXT,
    document_urls TEXT[]
);

-- DasWos Coins Transactions table (user-owned balance system)
CREATE TABLE daswos_coins_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    amount INTEGER NOT NULL, -- Positive for add, negative for spend
    transaction_type TEXT NOT NULL, -- "purchase", "spend", "refund", "bonus"
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed',
    metadata JSONB DEFAULT '{}',
    wallet_id TEXT, -- Which wallet was used for this transaction (for audit)
    related_order_id INTEGER,
    related_split_buy_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- User Product Content table (for user-generated content)
CREATE TABLE user_product_content (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    content_type TEXT NOT NULL, -- "review", "question", "answer", "image"
    content TEXT NOT NULL,
    rating INTEGER, -- For reviews
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- App Settings table (for application configuration)
CREATE TABLE app_settings (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Robot Positioning Algorithm Tables
-- Added for database-driven robot positioning with hourly rotation

-- Create robot positioning algorithms table
CREATE TABLE robot_positioning_algorithms (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    positions JSONB NOT NULL, -- Array of {x, y} position objects (1200+ positions)
    screen_width INTEGER NOT NULL DEFAULT 1920, -- Target screen width for positions
    screen_height INTEGER NOT NULL DEFAULT 1080, -- Target screen height for positions
    total_positions INTEGER NOT NULL, -- Number of positions in the algorithm (should be 1200+)
    is_active BOOLEAN NOT NULL DEFAULT true, -- Whether this algorithm is available for assignment
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Create user algorithm assignments table
CREATE TABLE user_algorithm_assignments (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL, -- Changed to TEXT to support both numeric user IDs and session IDs
    algorithm_id INTEGER NOT NULL,
    current_position_index INTEGER NOT NULL DEFAULT 0, -- Current position index in the algorithm sequence
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- When this assignment expires (1 hour from assignment)
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Create algorithm rotation log table
CREATE TABLE algorithm_rotation_log (
    id SERIAL PRIMARY KEY,
    rotation_time TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    total_users INTEGER NOT NULL, -- Number of users that were reassigned
    total_algorithms INTEGER NOT NULL, -- Number of algorithms available
    rotation_details JSONB NOT NULL, -- JSON object with user->algorithm mapping details
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add Foreign Key Constraints
ALTER TABLE users ADD CONSTRAINT fk_users_family_owner FOREIGN KEY (family_owner_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE users ADD CONSTRAINT fk_users_parent_account FOREIGN KEY (parent_account_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE categories ADD CONSTRAINT fk_categories_parent FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL;
ALTER TABLE products ADD CONSTRAINT fk_products_seller FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE products ADD CONSTRAINT fk_products_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;
ALTER TABLE user_sessions ADD CONSTRAINT fk_user_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_session FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE CASCADE;
ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
ALTER TABLE purchases ADD CONSTRAINT fk_purchases_buyer FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE purchases ADD CONSTRAINT fk_purchases_seller FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE purchases ADD CONSTRAINT fk_purchases_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
ALTER TABLE seller_verification ADD CONSTRAINT fk_seller_verification_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE daswos_coins_transactions ADD CONSTRAINT fk_daswos_coins_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_product_content ADD CONSTRAINT fk_user_product_content_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_product_content ADD CONSTRAINT fk_user_product_content_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

-- Add foreign key constraints for robot positioning tables
-- Note: user_id constraint removed since it can now be session IDs that don't exist in users table
ALTER TABLE user_algorithm_assignments ADD CONSTRAINT fk_user_algorithm_assignments_algorithm FOREIGN KEY (algorithm_id) REFERENCES robot_positioning_algorithms(id) ON DELETE CASCADE;

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_family_owner ON users(family_owner_id);

-- Create case-insensitive unique indexes for username and email to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username_unique_ci ON users (LOWER(username));
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email_unique_ci ON users (LOWER(email));
CREATE INDEX IF NOT EXISTS idx_products_seller ON products(seller_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_cart_items_user ON cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_session ON cart_items(session_id);
CREATE INDEX IF NOT EXISTS idx_purchases_buyer ON purchases(buyer_id);
CREATE INDEX IF NOT EXISTS idx_purchases_seller ON purchases(seller_id);
CREATE INDEX IF NOT EXISTS idx_daswos_coins_user ON daswos_coins_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_daswos_coins_type ON daswos_coins_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_daswos_coins_created ON daswos_coins_transactions(created_at);

-- Create indexes for robot positioning tables
CREATE INDEX IF NOT EXISTS idx_robot_algorithms_active ON robot_positioning_algorithms(is_active);
CREATE INDEX IF NOT EXISTS idx_user_assignments_user_active ON user_algorithm_assignments(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_user_assignments_expires ON user_algorithm_assignments(expires_at);
CREATE INDEX IF NOT EXISTS idx_rotation_log_time ON algorithm_rotation_log(rotation_time);

-- Create Functions and Triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cart_items_updated_at BEFORE UPDATE ON cart_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_information_content_updated_at BEFORE UPDATE ON information_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchases_updated_at BEFORE UPDATE ON purchases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_product_content_updated_at BEFORE UPDATE ON user_product_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_app_settings_updated_at BEFORE UPDATE ON app_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add triggers for robot positioning tables
CREATE TRIGGER update_robot_algorithms_updated_at BEFORE UPDATE ON robot_positioning_algorithms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create search function for products
CREATE OR REPLACE FUNCTION search_products(search_term TEXT)
RETURNS SETOF products AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM products
    WHERE
        to_tsvector('english', title || ' ' || COALESCE(description, '')) @@ websearch_to_tsquery('english', search_term)
        OR search_term = ANY(tags)
        OR title ILIKE '%' || search_term || '%'
        OR description ILIKE '%' || search_term || '%';
END;
$$ LANGUAGE plpgsql;

-- Insert sample categories
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices and accessories'),
    ('Clothing', 'clothing', 'Apparel and fashion items'),
    ('Home & Garden', 'home-garden', 'Home improvement and gardening supplies'),
    ('Sports', 'sports', 'Sports equipment and gear'),
    ('Toys', 'toys', 'Toys and games');

-- Insert sample admin user
INSERT INTO users (username, password, email, full_name, is_admin, is_seller, trust_score) VALUES
    ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true, true, 100);

-- Insert sample products
INSERT INTO products (title, description, price, image_url, seller_id, seller_name, category_id, tags, trust_score, status, quantity) VALUES
    ('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 19999, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "audio", "wireless"}', 85, 'active', 10),
    ('Running Shoes', 'Lightweight running shoes for all terrains', 8999, 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop', 1, 'admin', 2, '{"footwear", "sports", "running"}', 85, 'active', 15),
    ('Smart Watch', 'Feature-rich smartwatch with health tracking', 24999, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "wearables", "fitness"}', 85, 'active', 8),
    ('Yoga Mat', 'Non-slip yoga mat for all skill levels', 2999, 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop', 1, 'admin', 4, '{"fitness", "yoga", "home"}', 85, 'active', 20),
    ('Blender', 'High-powered blender for smoothies and food prep', 12999, 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop', 1, 'admin', 3, '{"kitchen", "appliances"}', 85, 'active', 5);

-- Create view for backward compatibility with current-brobot-1
CREATE OR REPLACE VIEW products_legacy AS
SELECT
    id,
    title,
    description,
    CAST(price AS DECIMAL(10,2)) / 100 AS price, -- Convert cents to dollars
    image_url,
    category_id,
    tags,
    in_stock,
    created_at
FROM products
WHERE status = 'active';

-- Create view for categories with legacy compatibility
CREATE OR REPLACE VIEW categories_legacy AS
SELECT
    id,
    name,
    COALESCE(slug, LOWER(REPLACE(name, ' ', '-'))) as slug,
    description,
    created_at
FROM categories
WHERE is_active = true;

-- Fix existing products to appear in SafeSphere by setting proper identity verification
-- This will make all existing products appear in SafeSphere searches
UPDATE products
SET
  identity_verified = true,
  identity_verification_status = 'approved',
  trust_score = CASE
    WHEN trust_score < 70 THEN 75  -- Ensure minimum trust score for SafeSphere
    ELSE trust_score
  END
WHERE identity_verified = false OR identity_verification_status = 'none' OR identity_verification_status IS NULL;

-- Fix existing users to have proper SafeSphere defaults
UPDATE users
SET
  trust_score = CASE
    WHEN trust_score < 70 THEN 75  -- Ensure minimum trust score for SafeSphere
    ELSE trust_score
  END
WHERE trust_score < 70;

-- Success message
SELECT
    'UNIFIED SCHEMA SETUP COMPLETE!' as status,
    (SELECT COUNT(*) FROM users) as users_count,
    (SELECT COUNT(*) FROM categories) as categories_count,
    (SELECT COUNT(*) FROM products) as products_count,
    (SELECT COUNT(*) FROM user_sessions) as sessions_count,
    (SELECT COUNT(*) FROM cart_items) as cart_items_count,
    (SELECT COUNT(*) FROM information_content) as information_count,
    (SELECT COUNT(*) FROM purchases) as purchases_count,
    (SELECT COUNT(*) FROM seller_verification) as seller_verification_count,
    (SELECT COUNT(*) FROM daswos_coins_transactions) as transactions_count,
    (SELECT COUNT(*) FROM user_product_content) as user_content_count,
    (SELECT COUNT(*) FROM app_settings) as app_settings_count,
    (SELECT COUNT(*) FROM robot_positioning_algorithms) as robot_algorithms_count,
    (SELECT COUNT(*) FROM user_algorithm_assignments) as user_assignments_count,
    (SELECT COUNT(*) FROM algorithm_rotation_log) as rotation_log_count;
