import { useState, useEffect } from 'react';
import '../styles/DaswosAnimation.css';

// Import images
import daswosfront from '../assets/images/daswos_front.png';
import daswoshologram from '../assets/images/daswos_side_hologram.webp';

const DaswosAnimation = () => {
  const [animationState, setAnimationState] = useState<'front' | 'turning' | 'hologram'>('front');
  const [hologramOpacity, setHologramOpacity] = useState(0);
  const [rotationDegree, setRotationDegree] = useState(0);
  
  // Function to trigger the animation sequence
  const startAnimation = () => {
    // Reset to initial state if already in hologram state
    if (animationState === 'hologram') {
      setAnimationState('front');
      setHologramOpacity(0);
      setRotationDegree(0);
      return;
    }
    
    // Start turning animation
    setAnimationState('turning');
    
    // Animate rotation over time
    let degree = 0;
    const rotationInterval = setInterval(() => {
      degree += 2;
      setRotationDegree(degree);
      
      if (degree >= 90) {
        clearInterval(rotationInterval);
        setAnimationState('hologram');
        
        // Start hologram fade-in animation
        let opacity = 0;
        const hologramInterval = setInterval(() => {
          opacity += 0.05;
          setHologramOpacity(opacity);
          
          if (opacity >= 1) {
            clearInterval(hologramInterval);
          }
        }, 50);
      }
    }, 20);
  };
  
  return (
    <div className="daswos-container">
      <div className="animation-area">
        {/* Front-facing Daswos (visible when in front state) */}
        {animationState === 'front' && (
          <img 
            src={daswosfront} 
            alt="Daswos Robot Front" 
            className="daswos-image front"
            onClick={startAnimation}
          />
        )}
        
        {/* Turning Daswos (visible during transition) */}
        {animationState === 'turning' && (
          <div 
            className="daswos-turning"
            style={{ transform: `rotateY(${rotationDegree}deg)` }}
          >
            <img 
              src={daswosfront} 
              alt="Daswos Robot Turning" 
              className="daswos-image turning"
            />
          </div>
        )}
        
        {/* Side-facing Daswos with hologram (visible in hologram state) */}
        {animationState === 'hologram' && (
          <div className="hologram-container" onClick={startAnimation}>
            <img 
              src={daswoshologram} 
              alt="Daswos Robot with Hologram" 
              className="daswos-image hologram"
            />
            <div 
              className="hologram-projection" 
              style={{ opacity: hologramOpacity }}
            >
              <div className="hologram-content">
                <h2>DASWOS</h2>
                <p>Interactive AI Assistant</p>
                <div className="hologram-lines"></div>
              </div>
            </div>
          </div>
        )}
        
        <div className="instruction-text">
          {animationState === 'front' ? 'Click the robot to activate hologram' : 'Click to reset'}
        </div>
      </div>
    </div>
  );
};

export default DaswosAnimation;
