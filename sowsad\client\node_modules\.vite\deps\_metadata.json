{"hash": "40ddaf27", "configHash": "51d62093", "lockfileHash": "a9e10829", "browserHash": "9da89969", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "4949d768", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "a616e44f", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5c30b1ef", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5f873d22", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "9cd368dc", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "2fead0c0", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "763a85dc", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "e01a1abb", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "09d81da5", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "06d20740", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "b1b7b440", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "933c719a", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "ca59e026", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "ca99b3e0", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "4cd2ab0d", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "95e0ba53", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "2199d047", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "b0f8d2b3", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "b8b2ef68", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "30025087", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "d8d9c5e7", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "b07ce0d1", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "5b76d3f3", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "529af062", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "02ca3544", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "b73feae9", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "302df360", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "ad77fa09", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "705ba3d5", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "e996cd6d", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "6487d542", "needsInterop": false}, "react-p5": {"src": "../../../../node_modules/react-p5/build/index.js", "file": "react-p5.js", "fileHash": "e8f9ccac", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "786b3fcc", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "491ddd55", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "d40807a3", "needsInterop": false}}, "chunks": {"chunk-JSZFEFYX": {"file": "chunk-JSZFEFYX.js"}, "chunk-RJWUJD3P": {"file": "chunk-RJWUJD3P.js"}, "chunk-ELW6O2PE": {"file": "chunk-ELW6O2PE.js"}, "chunk-HLAPA55K": {"file": "chunk-HLAPA55K.js"}, "chunk-SCY5IBRC": {"file": "chunk-SCY5IBRC.js"}, "chunk-GUDGYPVW": {"file": "chunk-GUDGYPVW.js"}, "chunk-3R4G6BT7": {"file": "chunk-3R4G6BT7.js"}, "chunk-TTMIGO44": {"file": "chunk-TTMIGO44.js"}, "chunk-SHJTHTTR": {"file": "chunk-SHJTHTTR.js"}, "chunk-AVP7UVNA": {"file": "chunk-AVP7UVNA.js"}, "chunk-PTZ23AHU": {"file": "chunk-PTZ23AHU.js"}, "chunk-VU6CRJTE": {"file": "chunk-VU6CRJTE.js"}, "chunk-J2XLQQR2": {"file": "chunk-J2XLQQR2.js"}, "chunk-TR77JAFH": {"file": "chunk-TR77JAFH.js"}, "chunk-FREGBPUJ": {"file": "chunk-FREGBPUJ.js"}, "chunk-HFCPDRS2": {"file": "chunk-HFCPDRS2.js"}, "chunk-WWBVPQNB": {"file": "chunk-WWBVPQNB.js"}, "chunk-PHTQUGG6": {"file": "chunk-PHTQUGG6.js"}, "chunk-3NBNTOBL": {"file": "chunk-3NBNTOBL.js"}, "chunk-AVJPV5ZH": {"file": "chunk-AVJPV5ZH.js"}, "chunk-JYSI5OBP": {"file": "chunk-JYSI5OBP.js"}, "chunk-7URR3GLA": {"file": "chunk-7URR3GLA.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}