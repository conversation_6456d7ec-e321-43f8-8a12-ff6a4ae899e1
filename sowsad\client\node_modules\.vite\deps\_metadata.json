{"hash": "70090a6d", "configHash": "8f39d189", "lockfileHash": "a9e10829", "browserHash": "04f7f0f8", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "e5d4816d", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "5b39243d", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c5ac5ff4", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a510db97", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "54f893c8", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "7b58cdd1", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "bf6348cf", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "d28869dc", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "5332e0af", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "44f1e94a", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "3bca9cf1", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "bc165567", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "5595241b", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "e5223763", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "d582ff09", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "ad77bbc1", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "1a8ff00e", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "bf887142", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "25d95eb9", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "113a5742", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "df44de8e", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "184b3c69", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "d978f14d", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "65e0a5e3", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "efc827de", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "ccae34a7", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "cc1443f0", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "89da097a", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "55a50e12", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "94966299", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "91789b82", "needsInterop": false}, "react-p5": {"src": "../../../../node_modules/react-p5/build/index.js", "file": "react-p5.js", "fileHash": "67f81328", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "84a13902", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "082ba240", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "6d570939", "needsInterop": false}}, "chunks": {"chunk-2KCZN3IN": {"file": "chunk-2KCZN3IN.js"}, "chunk-M3MGFQBP": {"file": "chunk-M3MGFQBP.js"}, "chunk-UHLFGOB3": {"file": "chunk-UHLFGOB3.js"}, "chunk-UQF5EARV": {"file": "chunk-UQF5EARV.js"}, "chunk-XTAUCOQC": {"file": "chunk-XTAUCOQC.js"}, "chunk-HBKJJWUT": {"file": "chunk-HBKJJWUT.js"}, "chunk-MA223RL2": {"file": "chunk-MA223RL2.js"}, "chunk-53MQDUXC": {"file": "chunk-53MQDUXC.js"}, "chunk-SKI2UCHC": {"file": "chunk-SKI2UCHC.js"}, "chunk-HLHKKTXM": {"file": "chunk-HLHKKTXM.js"}, "chunk-OZHZ2IM7": {"file": "chunk-OZHZ2IM7.js"}, "chunk-QQGRI3EU": {"file": "chunk-QQGRI3EU.js"}, "chunk-C3M5WYXQ": {"file": "chunk-C3M5WYXQ.js"}, "chunk-3IUYUI3A": {"file": "chunk-3IUYUI3A.js"}, "chunk-CO6MU3Z7": {"file": "chunk-CO6MU3Z7.js"}, "chunk-UB6ZE4X2": {"file": "chunk-UB6ZE4X2.js"}, "chunk-7FY3LWCE": {"file": "chunk-7FY3LWCE.js"}, "chunk-FNUHTYUW": {"file": "chunk-FNUHTYUW.js"}, "chunk-LN47T4UX": {"file": "chunk-LN47T4UX.js"}, "chunk-F3OYNICX": {"file": "chunk-F3OYNICX.js"}, "chunk-PHDAYJMQ": {"file": "chunk-PHDAYJMQ.js"}, "chunk-NBSTMCL3": {"file": "chunk-NBSTMCL3.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}