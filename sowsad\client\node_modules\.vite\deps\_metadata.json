{"hash": "70090a6d", "configHash": "8f39d189", "lockfileHash": "a9e10829", "browserHash": "04f7f0f8", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "3794c1ec", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "285e812a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ce52ea42", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "fc7cfe00", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "a6f9758a", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "8b2f225e", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "f0681cab", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "c1d87cbb", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "17c87cf4", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "06b62662", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "14655516", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "59c09bd4", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "0b99ae9a", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "f6d6b124", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "0224de7f", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "d2775c12", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "187b3ae6", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "b867dba4", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "8f81f00f", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "e622bdee", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "789702fc", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "470b65ca", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "b4dfe057", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "be86b913", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "535a1e2b", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "8a01fd89", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "dd43fdeb", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "73e6dfdc", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8bbedb5d", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "9de27595", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "f10b0634", "needsInterop": false}, "react-p5": {"src": "../../../../node_modules/react-p5/build/index.js", "file": "react-p5.js", "fileHash": "1c0e7149", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c3fc10ce", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "9cd14e1d", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "31c1770f", "needsInterop": false}}, "chunks": {"chunk-2KCZN3IN": {"file": "chunk-2KCZN3IN.js"}, "chunk-MA223RL2": {"file": "chunk-MA223RL2.js"}, "chunk-NI3JHNNM": {"file": "chunk-NI3JHNNM.js"}, "chunk-KAF72SQA": {"file": "chunk-KAF72SQA.js"}, "chunk-UQF5EARV": {"file": "chunk-UQF5EARV.js"}, "chunk-XTAUCOQC": {"file": "chunk-XTAUCOQC.js"}, "chunk-HBKJJWUT": {"file": "chunk-HBKJJWUT.js"}, "chunk-QQGRI3EU": {"file": "chunk-QQGRI3EU.js"}, "chunk-AJUVNTZZ": {"file": "chunk-AJUVNTZZ.js"}, "chunk-T7YHF465": {"file": "chunk-T7YHF465.js"}, "chunk-RUTJ3L2W": {"file": "chunk-RUTJ3L2W.js"}, "chunk-Z5HXLDJI": {"file": "chunk-Z5HXLDJI.js"}, "chunk-JI27VNIC": {"file": "chunk-JI27VNIC.js"}, "chunk-QKI3G5ML": {"file": "chunk-QKI3G5ML.js"}, "chunk-PVOMQG6Z": {"file": "chunk-PVOMQG6Z.js"}, "chunk-UB6ZE4X2": {"file": "chunk-UB6ZE4X2.js"}, "chunk-MWRLGAH7": {"file": "chunk-MWRLGAH7.js"}, "chunk-FNUHTYUW": {"file": "chunk-FNUHTYUW.js"}, "chunk-LN47T4UX": {"file": "chunk-LN47T4UX.js"}, "chunk-F3OYNICX": {"file": "chunk-F3OYNICX.js"}, "chunk-PHDAYJMQ": {"file": "chunk-PHDAYJMQ.js"}, "chunk-NBSTMCL3": {"file": "chunk-NBSTMCL3.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}