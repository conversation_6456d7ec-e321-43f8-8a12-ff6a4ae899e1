{"hash": "70090a6d", "configHash": "8f39d189", "lockfileHash": "a9e10829", "browserHash": "04f7f0f8", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "673849ba", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "7c5f4a81", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "7dd5c308", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f0cf8dff", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "b4492706", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "b2c57d8a", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "31e66b94", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "f2721ea2", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "bcb5f094", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "3fc06130", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "3e5fabfa", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "6ccabb3a", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "37268352", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../../../node_modules/@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "8dc10758", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "e3948996", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "1e6d23f5", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "f4cceaa9", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "f5a9d496", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "2e7d860c", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "b2938d02", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../../../node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "01efc465", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../../../node_modules/@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "836a7019", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "e26d4050", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "10de708d", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "31fae015", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "838c01b2", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "f7ac8ec3", "needsInterop": false}, "react-beautiful-dnd": {"src": "../../../../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "file": "react-beautiful-dnd.js", "fileHash": "75fed6a8", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "dcdf8190", "needsInterop": true}, "react-helmet": {"src": "../../../../node_modules/react-helmet/es/Helmet.js", "file": "react-helmet.js", "fileHash": "91e23e25", "needsInterop": false}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "1a5fdbb1", "needsInterop": false}, "react-p5": {"src": "../../../../node_modules/react-p5/build/index.js", "file": "react-p5.js", "fileHash": "8d4c831a", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4f508a0c", "needsInterop": false}, "wouter": {"src": "../../../../node_modules/wouter/esm/index.js", "file": "wouter.js", "fileHash": "05a75a7c", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "3ccdd5e6", "needsInterop": false}}, "chunks": {"chunk-2KCZN3IN": {"file": "chunk-2KCZN3IN.js"}, "chunk-M3MGFQBP": {"file": "chunk-M3MGFQBP.js"}, "chunk-UQF5EARV": {"file": "chunk-UQF5EARV.js"}, "chunk-44B7UUG5": {"file": "chunk-44B7UUG5.js"}, "chunk-HBKJJWUT": {"file": "chunk-HBKJJWUT.js"}, "chunk-XTAUCOQC": {"file": "chunk-XTAUCOQC.js"}, "chunk-QQGRI3EU": {"file": "chunk-QQGRI3EU.js"}, "chunk-C3M5WYXQ": {"file": "chunk-C3M5WYXQ.js"}, "chunk-MA223RL2": {"file": "chunk-MA223RL2.js"}, "chunk-K22UQHFL": {"file": "chunk-K22UQHFL.js"}, "chunk-SKI2UCHC": {"file": "chunk-SKI2UCHC.js"}, "chunk-HLHKKTXM": {"file": "chunk-HLHKKTXM.js"}, "chunk-OZHZ2IM7": {"file": "chunk-OZHZ2IM7.js"}, "chunk-CO6MU3Z7": {"file": "chunk-CO6MU3Z7.js"}, "chunk-3IUYUI3A": {"file": "chunk-3IUYUI3A.js"}, "chunk-UB6ZE4X2": {"file": "chunk-UB6ZE4X2.js"}, "chunk-7FY3LWCE": {"file": "chunk-7FY3LWCE.js"}, "chunk-FNUHTYUW": {"file": "chunk-FNUHTYUW.js"}, "chunk-LN47T4UX": {"file": "chunk-LN47T4UX.js"}, "chunk-F3OYNICX": {"file": "chunk-F3OYNICX.js"}, "chunk-PHDAYJMQ": {"file": "chunk-PHDAYJMQ.js"}, "chunk-NBSTMCL3": {"file": "chunk-NBSTMCL3.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}