import { db } from '../db';
import { 
  robotPositioningAlgorithms, 
  userAlgorithmAssignments, 
  algorithmRotationLog,
  users,
  type RobotPositioningAlgorithm,
  type UserAlgorithmAssignment,
  type InsertRobotPositioningAlgorithm,
  type InsertUserAlgorithmAssignment,
  type InsertAlgorithmRotationLog
} from '@shared/schema';
import { eq, and, lt, desc, sql } from 'drizzle-orm';
import { log } from '../vite';

export interface Position {
  x: number;
  y: number;
}

export interface AlgorithmGenerationOptions {
  screenWidth?: number;
  screenHeight?: number;
  totalPositions?: number;
  minDistance?: number;
  safeMargin?: number;
}

export class RobotPositioningService {
  
  /**
   * Generate a full-screen coverage algorithm with 1200+ unique positions
   */
  static generateFullScreenAlgorithm(options: AlgorithmGenerationOptions = {}): Position[] {
    const {
      screenWidth = 1920,
      screenHeight = 1080,
      totalPositions = 1200,
      minDistance = 100, // Minimum distance between positions
      safeMargin = 120   // Margin from screen edges
    } = options;

    const positions: Position[] = [];
    const maxAttempts = totalPositions * 10; // Prevent infinite loops
    let attempts = 0;

    // Define usable area
    const minX = safeMargin;
    const maxX = screenWidth - safeMargin;
    const minY = safeMargin;
    const maxY = screenHeight - safeMargin;

    log(`🎲 Generating algorithm with ${totalPositions} positions for ${screenWidth}x${screenHeight} screen`);

    while (positions.length < totalPositions && attempts < maxAttempts) {
      // Generate random position within safe area
      const x = Math.random() * (maxX - minX) + minX;
      const y = Math.random() * (maxY - minY) + minY;

      // Check if position is far enough from existing positions
      let validPosition = true;
      for (const existingPos of positions) {
        const distance = Math.sqrt(
          Math.pow(x - existingPos.x, 2) + Math.pow(y - existingPos.y, 2)
        );
        if (distance < minDistance) {
          validPosition = false;
          break;
        }
      }

      if (validPosition) {
        positions.push({ x: Math.round(x), y: Math.round(y) });
      }

      attempts++;
    }

    log(`🎲 Generated ${positions.length} positions after ${attempts} attempts`);
    return positions;
  }

  /**
   * Create a new positioning algorithm in the database
   */
  static async createAlgorithm(
    name: string, 
    options: AlgorithmGenerationOptions = {}
  ): Promise<RobotPositioningAlgorithm> {
    const positions = this.generateFullScreenAlgorithm(options);
    
    const algorithmData: InsertRobotPositioningAlgorithm = {
      name,
      positions: positions as any, // JSONB field
      screenWidth: options.screenWidth || 1920,
      screenHeight: options.screenHeight || 1080,
      totalPositions: positions.length,
      isActive: true
    };

    const [algorithm] = await db
      .insert(robotPositioningAlgorithms)
      .values(algorithmData)
      .returning();

    log(`✅ Created algorithm "${name}" with ${positions.length} positions`);
    return algorithm;
  }

  /**
   * Get all active algorithms
   */
  static async getActiveAlgorithms(): Promise<RobotPositioningAlgorithm[]> {
    return await db
      .select()
      .from(robotPositioningAlgorithms)
      .where(eq(robotPositioningAlgorithms.isActive, true))
      .orderBy(robotPositioningAlgorithms.createdAt);
  }

  /**
   * Get user's current algorithm assignment
   */
  static async getUserAlgorithmAssignment(userId: number): Promise<UserAlgorithmAssignment | null> {
    const [assignment] = await db
      .select()
      .from(userAlgorithmAssignments)
      .where(
        and(
          eq(userAlgorithmAssignments.userId, userId),
          eq(userAlgorithmAssignments.isActive, true)
        )
      )
      .orderBy(desc(userAlgorithmAssignments.assignedAt))
      .limit(1);

    return assignment || null;
  }

  /**
   * Assign an algorithm to a user
   */
  static async assignAlgorithmToUser(
    userId: number, 
    algorithmId: number
  ): Promise<UserAlgorithmAssignment> {
    // Deactivate any existing assignments for this user
    await db
      .update(userAlgorithmAssignments)
      .set({ isActive: false })
      .where(eq(userAlgorithmAssignments.userId, userId));

    // Create new assignment (expires in 1 hour)
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    const assignmentData: InsertUserAlgorithmAssignment = {
      userId,
      algorithmId,
      currentPositionIndex: 0,
      expiresAt
    };

    const [assignment] = await db
      .insert(userAlgorithmAssignments)
      .values(assignmentData)
      .returning();

    log(`🎯 Assigned algorithm ${algorithmId} to user ${userId}, expires at ${expiresAt.toISOString()}`);
    return assignment;
  }

  /**
   * Get next position for a user (increments their position index)
   */
  static async getNextPositionForUser(userId: number, screenWidth: number, screenHeight: number): Promise<Position | null> {
    // Get user's current assignment
    const assignment = await this.getUserAlgorithmAssignment(userId);
    
    if (!assignment) {
      log(`❌ No algorithm assignment found for user ${userId}`);
      return null;
    }

    // Check if assignment has expired
    if (new Date() > assignment.expiresAt) {
      log(`⏰ Algorithm assignment expired for user ${userId}, needs rotation`);
      return null;
    }

    // Get the algorithm
    const [algorithm] = await db
      .select()
      .from(robotPositioningAlgorithms)
      .where(eq(robotPositioningAlgorithms.id, assignment.algorithmId))
      .limit(1);

    if (!algorithm) {
      log(`❌ Algorithm ${assignment.algorithmId} not found`);
      return null;
    }

    const positions = algorithm.positions as Position[];
    const currentIndex = assignment.currentPositionIndex;

    if (currentIndex >= positions.length) {
      log(`⚠️ User ${userId} has exhausted all positions in algorithm ${assignment.algorithmId}`);
      // Reset to beginning of algorithm
      await db
        .update(userAlgorithmAssignments)
        .set({ currentPositionIndex: 0 })
        .where(eq(userAlgorithmAssignments.id, assignment.id));
      
      return positions[0] ? this.scalePosition(positions[0], algorithm, screenWidth, screenHeight) : null;
    }

    // Get current position and increment index for next time
    const position = positions[currentIndex];
    
    await db
      .update(userAlgorithmAssignments)
      .set({ currentPositionIndex: currentIndex + 1 })
      .where(eq(userAlgorithmAssignments.id, assignment.id));

    log(`🎯 User ${userId} position ${currentIndex + 1}/${positions.length}: ${JSON.stringify(position)}`);
    
    return this.scalePosition(position, algorithm, screenWidth, screenHeight);
  }

  /**
   * Scale position from algorithm's target screen size to user's actual screen size
   */
  private static scalePosition(
    position: Position, 
    algorithm: RobotPositioningAlgorithm, 
    userScreenWidth: number, 
    userScreenHeight: number
  ): Position {
    const scaleX = userScreenWidth / algorithm.screenWidth;
    const scaleY = userScreenHeight / algorithm.screenHeight;

    return {
      x: Math.round(position.x * scaleX),
      y: Math.round(position.y * scaleY)
    };
  }

  /**
   * Initialize algorithms if none exist
   */
  static async initializeAlgorithms(): Promise<void> {
    const existingAlgorithms = await this.getActiveAlgorithms();
    
    if (existingAlgorithms.length === 0) {
      log('🎲 No algorithms found, creating initial set...');
      
      // Create 10 different algorithms for variety
      const algorithmNames = [
        'Random Scatter Alpha',
        'Grid Variation Beta', 
        'Spiral Pattern Gamma',
        'Cluster Formation Delta',
        'Edge Preference Epsilon',
        'Center Avoidance Zeta',
        'Corner Focus Eta',
        'Diagonal Sweep Theta',
        'Radial Distribution Iota',
        'Chaos Theory Kappa'
      ];

      for (const name of algorithmNames) {
        await this.createAlgorithm(name, {
          screenWidth: 1920,
          screenHeight: 1080,
          totalPositions: 1200,
          minDistance: 100,
          safeMargin: 120
        });
      }

      log(`✅ Created ${algorithmNames.length} initial algorithms`);
    }
  }

  /**
   * Rotate algorithms between users (called every hour)
   */
  static async rotateAlgorithms(): Promise<void> {
    log('🔄 Starting algorithm rotation...');

    // Get all active algorithms
    const algorithms = await this.getActiveAlgorithms();
    if (algorithms.length === 0) {
      log('❌ No algorithms available for rotation');
      return;
    }

    // Get all users who have active assignments
    const activeAssignments = await db
      .select({
        userId: userAlgorithmAssignments.userId,
        algorithmId: userAlgorithmAssignments.algorithmId
      })
      .from(userAlgorithmAssignments)
      .where(eq(userAlgorithmAssignments.isActive, true));

    if (activeAssignments.length === 0) {
      log('ℹ️ No active assignments to rotate');
      return;
    }

    // Create a shuffled assignment mapping
    const userIds = activeAssignments.map(a => a.userId);
    const algorithmIds = algorithms.map(a => a.id);

    // Shuffle algorithms to create new assignments
    const shuffledAlgorithmIds = [...algorithmIds].sort(() => Math.random() - 0.5);

    const rotationDetails: Record<string, any> = {};
    const newAssignments: Array<{ userId: number; algorithmId: number }> = [];

    // Assign algorithms to users in round-robin fashion
    for (let i = 0; i < userIds.length; i++) {
      const userId = userIds[i];
      const algorithmId = shuffledAlgorithmIds[i % shuffledAlgorithmIds.length];

      newAssignments.push({ userId, algorithmId });
      rotationDetails[`user_${userId}`] = {
        oldAlgorithm: activeAssignments.find(a => a.userId === userId)?.algorithmId,
        newAlgorithm: algorithmId
      };
    }

    // Deactivate all current assignments
    await db
      .update(userAlgorithmAssignments)
      .set({ isActive: false })
      .where(eq(userAlgorithmAssignments.isActive, true));

    // Create new assignments
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    for (const { userId, algorithmId } of newAssignments) {
      await db
        .insert(userAlgorithmAssignments)
        .values({
          userId,
          algorithmId,
          currentPositionIndex: 0,
          expiresAt
        });
    }

    // Log the rotation
    await db
      .insert(algorithmRotationLog)
      .values({
        rotationTime: new Date(),
        totalUsers: userIds.length,
        totalAlgorithms: algorithms.length,
        rotationDetails
      });

    log(`✅ Algorithm rotation completed: ${userIds.length} users reassigned across ${algorithms.length} algorithms`);
  }

  /**
   * Ensure user has an algorithm assignment (creates one if needed)
   */
  static async ensureUserHasAlgorithm(userId: number): Promise<UserAlgorithmAssignment | null> {
    // Check if user has active assignment
    let assignment = await this.getUserAlgorithmAssignment(userId);

    if (assignment && new Date() <= assignment.expiresAt) {
      return assignment; // Valid assignment exists
    }

    // Need to assign an algorithm
    const algorithms = await this.getActiveAlgorithms();
    if (algorithms.length === 0) {
      log('❌ No algorithms available to assign');
      return null;
    }

    // Pick a random algorithm
    const randomAlgorithm = algorithms[Math.floor(Math.random() * algorithms.length)];

    return await this.assignAlgorithmToUser(userId, randomAlgorithm.id);
  }

  /**
   * Get algorithm statistics
   */
  static async getAlgorithmStats(): Promise<{
    totalAlgorithms: number;
    activeAssignments: number;
    lastRotation: Date | null;
  }> {
    const algorithms = await this.getActiveAlgorithms();

    const activeAssignments = await db
      .select({ count: sql<number>`count(*)` })
      .from(userAlgorithmAssignments)
      .where(eq(userAlgorithmAssignments.isActive, true));

    const [lastRotationLog] = await db
      .select()
      .from(algorithmRotationLog)
      .orderBy(desc(algorithmRotationLog.rotationTime))
      .limit(1);

    return {
      totalAlgorithms: algorithms.length,
      activeAssignments: activeAssignments[0]?.count || 0,
      lastRotation: lastRotationLog?.rotationTime || null
    };
  }
}
