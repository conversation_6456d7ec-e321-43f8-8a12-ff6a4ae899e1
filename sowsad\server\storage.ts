import connectPg from "connect-pg-simple";
import session from "express-session";
import memorystore from "memorystore";
import { db } from "./db";
import {
  and, asc, count, desc, eq, gte, inArray, isNotNull, isNull, lte, or, sql,
  ilike, like, alias, not
} from "drizzle-orm";
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { log } from "./vite";
import * as schema from '@shared/schema';
import {
  users, products, informationContent, collaborativeSearches, collaborativeResources, collaborativeCollaborators,
  resourcePermissionRequests, cartItems, splitBuys, splitBuyParticipants, orders, orderItems,
  daswosAiChats, daswosAiChatMessages, daswosAiSources, userSessions, familyInvitationCodes,
  purchases, dasWosCoinsTransactions, userPaymentMethods, userDasbarPreferences, searchQueries,
  sellerVerifications, bulkBuyRequests, appSettings, categories, aiShopperRecommendations
} from '@shared/schema';
import { userPurchaseHistory, userSearchHistory, userProductPreferences } from '@shared/user-history-schema';
import { userSubscriptions } from '@shared/user-subscriptions';

// Base table types
type User = typeof users.$inferSelect;
type InsertUser = typeof users.$inferInsert;
type Product = typeof products.$inferSelect;
type InsertProduct = typeof products.$inferInsert;
type InformationContent = typeof informationContent.$inferSelect;
type InsertInformationContent = typeof informationContent.$inferInsert;
type AiShopperRecommendation = typeof aiShopperRecommendations.$inferSelect;
type InsertAiShopperRecommendation = typeof aiShopperRecommendations.$inferInsert;
type Purchase = typeof purchases.$inferSelect;
type InsertPurchase = typeof purchases.$inferInsert;
type Order = typeof orders.$inferSelect;
type InsertOrder = typeof orders.$inferInsert;
type OrderItem = typeof orderItems.$inferSelect;
type InsertOrderItem = typeof orderItems.$inferInsert;
type CartItem = typeof cartItems.$inferSelect;
type InsertCartItem = typeof cartItems.$inferInsert;
type CartItemWithProduct = CartItem & { product: Product };
type SplitBuy = typeof splitBuys.$inferSelect;
type InsertSplitBuy = typeof splitBuys.$inferInsert;
type SplitBuyParticipant = typeof splitBuyParticipants.$inferSelect;
type InsertSplitBuyParticipant = typeof splitBuyParticipants.$inferInsert;
type UserPaymentMethod = typeof userPaymentMethods.$inferSelect;
type InsertUserPaymentMethod = typeof userPaymentMethods.$inferInsert;
type UserDasbarPreferences = typeof userDasbarPreferences.$inferSelect;
type InsertUserDasbarPreferences = typeof userDasbarPreferences.$inferInsert;
type CollaborativeSearch = typeof collaborativeSearches.$inferSelect;
type InsertCollaborativeSearch = typeof collaborativeSearches.$inferInsert;
type CollaborativeResource = typeof collaborativeResources.$inferSelect;
type InsertCollaborativeResource = typeof collaborativeResources.$inferInsert;
type CollaborativeCollaborator = typeof collaborativeCollaborators.$inferSelect;
type InsertCollaborativeCollaborator = typeof collaborativeCollaborators.$inferInsert;
type ResourcePermissionRequest = typeof resourcePermissionRequests.$inferSelect;
type InsertResourcePermissionRequest = typeof resourcePermissionRequests.$inferInsert;
type DaswosAiChat = typeof daswosAiChats.$inferSelect;
type InsertDaswosAiChat = typeof daswosAiChats.$inferInsert;
type DaswosAiChatMessage = typeof daswosAiChatMessages.$inferSelect;
type InsertDaswosAiChatMessage = typeof daswosAiChatMessages.$inferInsert;
type DaswosAiSource = typeof daswosAiSources.$inferSelect;
type InsertDaswosAiSource = typeof daswosAiSources.$inferInsert;
type UserSession = typeof userSessions.$inferSelect;
type InsertUserSession = typeof userSessions.$inferInsert;
type FamilyInvitationCode = typeof familyInvitationCodes.$inferSelect;
type InsertFamilyInvitationCode = typeof familyInvitationCodes.$inferInsert;
type SearchQuery = typeof searchQueries.$inferSelect;
type InsertSearchQuery = typeof searchQueries.$inferInsert;
type SellerVerification = typeof sellerVerifications.$inferSelect;
type InsertSellerVerification = typeof sellerVerifications.$inferInsert;
type BulkBuyRequest = typeof bulkBuyRequests.$inferSelect;
type InsertBulkBuyRequest = typeof bulkBuyRequests.$inferInsert;
type DasWosCoinsTransaction = typeof dasWosCoinsTransactions.$inferSelect;
type InsertDasWosCoinsTransaction = typeof dasWosCoinsTransactions.$inferInsert;

// Extended types
interface UserWithSubscription extends User {
  subscription?: {
    type: string;
    expiresAt: Date | null;
  };
}

// DasWos Wallet Generation Utilities
interface DasWosWalletCredentials {
  walletId: string;
  walletPassword: string;
}

function generateDasWosWalletCredentials(): DasWosWalletCredentials {
  // Generate a unique wallet ID (8-12 characters, alphanumeric)
  const walletId = 'daswos-' + crypto.randomBytes(4).toString('hex');

  // Generate a secure wallet password (16 characters, mixed case + numbers + symbols)
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let walletPassword = '';
  for (let i = 0; i < 16; i++) {
    walletPassword += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return { walletId, walletPassword };
}

interface PurchaseWithItems extends Purchase {
  items: {
    product: Product;
    quantity: number;
    price: number;
  }[];
}

interface OrderWithItems extends Order {
  items: OrderItem[];
}

interface SplitBuyWithParticipants extends SplitBuy {
  participants: SplitBuyParticipant[];
  createdBy: User;
  product: Product;
}

interface DaswosAiChatWithMessages extends DaswosAiChat {
  messages: DaswosAiChatMessage[];
}

interface PurchaseHistoryItem {
  id: number;
  userId: number;
  productId: number;
  quantity: number;
  price: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface SearchHistoryItem {
  id: number;
  userId: number;
  query: string;
  resultsCount: number;
  createdAt: Date;
}

interface UserSubscription {
  id: number;
  userId: number;
  type: string;
  status: string;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Simplified FallbackStorage class that implements the streamlined IStorage interface
export class FallbackStorage implements IStorage {
  public sessionStore: session.Store;

  // Class properties
  private users: User[] = [];
  private products: Product[] = [];
  private informationContent: InformationContent[] = [];
  private purchaseHistory: PurchaseHistoryItem[] = [];
  private searchHistory: SearchHistoryItem[] = [];
  private userSubscriptions: UserSubscription[] = [];
  private dasWosCoinsTransactions: any[] = [];
  private purchases: any[] = [];
  private userDasbarPreferencesCache: Record<number, any> = {};

  constructor() {
    // Initialize a memory store for sessions
    const MemoryStore = memorystore(session);
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    });

    // Initialize with sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Initialize with empty arrays - we'll use the database for all data
    this.users = [];
    this.userSubscriptions = [];
    this.dasWosCoinsTransactions = [];
    this.purchases = [];

    // Add sample products for fallback when database is unavailable
    this.products = [
      {
        id: 1,
        title: "High-Performance Laptop",
        description: "Capture stunning photos and videos with ease.",
        price: 2393,
        imageUrl: "https://images.unsplash.com/photo-1620864387532-68f02931a28a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxsYXB0b3B8ZW58MHwwfHx8MTY5NDA5NzI2Mnww&ixlib=rb-4.0.3&q=80&w=400",
        categoryId: 4,
        sellerId: 1,
        quantity: 10,
        soldQuantity: 0,
        status: "active",
        tags: ["laptop", "computer", "technology", "high-performance"],
        sellerVerified: true,
        trustScore: 100,
        identityVerified: true,
        identityVerificationStatus: "approved",
        isBulkBuy: false,
        sphere: "opensphere",
        createdAt: new Date(),
        updatedAt: new Date()
      } as Product & { sphere: string },
      {
        id: 2,
        title: "Luxury Leather Wallet",
        description: "Premium leather wallet with multiple card slots.",
        price: 89,
        imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHx3YWxsZXR8ZW58MHwwfHx8MTY5NDA5NzI2Mnww&ixlib=rb-4.0.3&q=80&w=400",
        categoryId: 2,
        sellerId: 2,
        quantity: 25,
        soldQuantity: 0,
        status: "active",
        tags: ["wallet", "leather", "luxury", "accessories"],
        sellerVerified: true,
        trustScore: 85,
        identityVerified: true,
        identityVerificationStatus: "approved",
        isBulkBuy: false,
        sphere: "opensphere",
        createdAt: new Date(),
        updatedAt: new Date()
      } as Product & { sphere: string },
      {
        id: 3,
        title: "Ergonomic Office Chair",
        description: "Comfortable office chair with lumbar support.",
        price: 299,
        imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxjaGFpcnxlbnwwfDB8fHwxNjk0MDk3MjYyfDA&ixlib=rb-4.0.3&q=80&w=400",
        categoryId: 3,
        sellerId: 3,
        quantity: 15,
        soldQuantity: 0,
        status: "active",
        tags: ["chair", "office", "ergonomic", "furniture"],
        sellerVerified: true,
        trustScore: 92,
        identityVerified: true,
        identityVerificationStatus: "approved",
        isBulkBuy: true,
        sphere: "opensphere",
        createdAt: new Date(),
        updatedAt: new Date()
      } as Product & { sphere: string }
    ];

    this.informationContent = [];
  }

  // Test connection for fallback storage (always succeeds)
  async testConnection(): Promise<void> {
    // Fallback storage is always available (in-memory)
    return Promise.resolve();
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.find(user => user.id === id);
  }

  async getUserById(id: number): Promise<User | undefined> {
    return this.getUser(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.users.find(user => user.username.toLowerCase() === username.toLowerCase());
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return this.users.find(user => user.email.toLowerCase() === email.toLowerCase());
  }

  async createUser(user: InsertUser): Promise<User> {
    // Generate DasWos wallet credentials for the new user
    const walletCredentials = generateDasWosWalletCredentials();

    // Create a complete user object with all required fields
    const newUser = {
      ...user,
      id: this.users.length + 1,
      fullName: (user as any).fullName || 'New User',
      createdAt: new Date(),
      updatedAt: new Date(),
      isSeller: false,
      isAdmin: false,
      avatar: '',
      hasSubscription: false,
      subscriptionType: 'limited',
      subscriptionExpiresAt: null,
      isFamilyOwner: false,
      familyOwnerId: null,
      isChild: false,
      isVerified: true,
      verificationToken: null,
      resetPasswordToken: null,
      resetPasswordExpires: null,
      lastLoginAt: new Date(),
      dasWosCoins: 0,
      // Assign the generated wallet ID to the user
      walletId: walletCredentials.walletId
    } as unknown as User;

    this.users.push(newUser);

    // Create the wallet entry in the wallets database (without password initially)
    try {
      await this.createWalletEntry(walletCredentials.walletId, newUser.id, newUser.username);
      console.log(`✅ Wallet entry created in wallets database for wallet ID: ${walletCredentials.walletId}`);
    } catch (error) {
      console.error(`❌ Failed to create wallet entry for ${walletCredentials.walletId}:`, error);
      // Don't fail user creation if wallet entry creation fails
    }

    // Log the wallet credentials for the user (in production, this should be sent securely to the user)
    console.log(`🎯 NEW USER WALLET CREDENTIALS:
    User: ${newUser.username} (ID: ${newUser.id})
    Wallet ID: ${walletCredentials.walletId}
    Wallet Password: ${walletCredentials.walletPassword}
    ⚠️  IMPORTANT: User must save these credentials to access their DasWos coins!`);

    return newUser;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return undefined;
    }

    // Update the user with the provided updates
    const updatedUser = {
      ...this.users[userIndex],
      ...updates,
      updatedAt: new Date()
    } as User;

    this.users[userIndex] = updatedUser;
    return updatedUser;
  }

  // User subscription operations
  async updateUserSubscription(userId: number, subscriptionType: string, durationMonths: number): Promise<User> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const expiresAt = new Date();
    expiresAt.setMonth(expiresAt.getMonth() + durationMonths);

    // Update user's subscription info
    const updatedUser = await this.updateUser(userId, {
      hasSubscription: true,
      subscriptionType,
      subscriptionExpiresAt: expiresAt
    });

    if (!updatedUser) {
      throw new Error('Failed to update user subscription');
    }

    return updatedUser;
  }

  async checkUserHasSubscription(userId: number): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    if (!user.hasSubscription) return false;

    // Check if subscription is expired
    if (user.subscriptionExpiresAt && new Date(user.subscriptionExpiresAt) < new Date()) {
      // Update user's subscription status if expired
      await this.updateUser(userId, { hasSubscription: false });
      return false;
    }

    return true;
  }

  async getUserSubscriptionDetails(userId: number): Promise<{hasSubscription: boolean, type?: string, expiresAt?: Date, billingCycle?: string}> {
    const hasSubscription = await this.checkUserHasSubscription(userId);
    if (!hasSubscription) {
      return { hasSubscription: false };
    }

    const user = await this.getUser(userId);
    return {
      hasSubscription: true,
      type: user?.subscriptionType,
      expiresAt: user?.subscriptionExpiresAt || undefined,
      billingCycle: 'monthly' // Default billing cycle
    };
  }

  // DasWos Coins operations
  async getUserDasWosCoins(userId: number): Promise<number> {
    const user = await this.getUser(userId);
    if (!user) return 0;
    return user.dasWosCoins || 0;
  }

  async addDasWosCoins(userId: number, amount: number, type: string, description: string, metadata: any = {}, walletId?: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    const newBalance = (user.dasWosCoins || 0) + amount;

    // Update user's balance
    await this.updateUser(userId, { dasWosCoins: newBalance });

    // Record transaction
    this.dasWosCoinsTransactions.push({
      id: this.dasWosCoinsTransactions.length + 1,
      userId,
      amount,
      type,
      description,
      metadata,
      walletId: walletId || null,
      balance: newBalance,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return true;
  }

  async spendDasWosCoins(userId: number, amount: number, description: string, metadata: any = {}, walletId?: string): Promise<boolean> {
    const currentBalance = await this.getUserDasWosCoins(userId);
    if (currentBalance < amount) {
      return false; // Insufficient balance
    }

    return this.addDasWosCoins(userId, -amount, 'spend', description, metadata, walletId);
  }

  async getDasWosCoinsTransactions(userId: number, limit: number = 50): Promise<any[]> {
    return this.dasWosCoinsTransactions
      .filter(tx => tx.userId === userId)
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, limit);
  }

  // Create wallet entry in wallets database (without password initially)
  async createWalletEntry(walletId: string, userId: number, username: string): Promise<void> {
    try {
      console.log(`🏦 [FALLBACK] Creating wallet entry in wallets database:
      Wallet ID: ${walletId}
      User ID: ${userId}
      Username: ${username}
      Status: Ready for password creation`);

      // Even in fallback mode, we should try to create the wallet entry
      // Import Supabase client for wallet database
      const { createClient } = await import('@supabase/supabase-js');

      // Wallet database configuration
      const WALLET_SUPABASE_URL = 'https://mjyaqqsxhkqyzqufpxzl.supabase.co';
      const WALLET_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs';

      // Create Supabase client for wallet database
      const walletClient = createClient(WALLET_SUPABASE_URL, WALLET_SUPABASE_KEY);

      // 1. Insert into wallets table (without password_hash initially)
      // Don't include password_hash field at all to avoid NOT NULL constraint
      const { data: walletData, error: walletError } = await walletClient
        .from('wallets')
        .insert({
          wallet_id: walletId,
          is_active: true, // Active immediately so user can see balance
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (walletError) {
        console.error(`❌ [FALLBACK] Failed to insert wallet ${walletId}:`, walletError);
        throw new Error(`Failed to create wallet entry: ${walletError.message}`);
      }

      // 2. Insert into wallet_connections table to link wallet to user
      const { data: connectionData, error: connectionError } = await walletClient
        .from('wallet_connections')
        .insert({
          wallet_id: walletId,
          user_id: userId,
          username: username,
          connected_at: new Date().toISOString(),
          is_active: true
        })
        .select()
        .single();

      if (connectionError) {
        console.error(`❌ [FALLBACK] Failed to create wallet connection for ${walletId}:`, connectionError);
        // Don't throw here - wallet was created successfully, connection is secondary
      }

      console.log(`✅ [FALLBACK] Successfully created wallet entry in database:
      Wallet ID: ${walletId}
      Database ID: ${walletData?.id}
      Connection: ${connectionData ? 'Created' : 'Failed'}
      Status: Ready for password creation`);

    } catch (error) {
      console.error(`❌ [FALLBACK] Failed to create wallet entry for ${walletId}:`, error);
      throw error;
    }
  }

  // Cart operations
  private cartItems: any[] = [];

  async getUserCartItems(userId: number): Promise<CartItemWithProduct[]> {
    return this.cartItems
      .filter(item => item.userId === userId)
      .map(item => ({
        ...item,
        product: this.products.find(p => p.id === item.productId) || null
      }));
  }

  async addCartItem(item: InsertCartItem): Promise<CartItem> {
    const existingItem = this.cartItems.find(
      i => i.userId === item.userId && i.productId === item.productId
    );

    const newItem = {
      ...item,
      id: this.cartItems.length + 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    if (existingItem) {
      // Update quantity if item already exists in cart
      existingItem.quantity += item.quantity;
      existingItem.updatedAt = new Date();
      return existingItem;
    } else {
      this.cartItems.push(newItem);
      return newItem;
    }
  }

  async updateCartItemQuantity(itemId: number, quantity: number): Promise<CartItem> {
    const item = this.cartItems.find(i => i.id === itemId);
    if (!item) {
      throw new Error('Cart item not found');
    }

    item.quantity = quantity;
    item.updatedAt = new Date();
    return item;
  }

  async removeCartItem(itemId: number): Promise<boolean> {
    const initialLength = this.cartItems.length;
    this.cartItems = this.cartItems.filter(item => item.id !== itemId);
    return this.cartItems.length < initialLength;
  }

  async clearUserCart(userId: number): Promise<boolean> {
    const initialLength = this.cartItems.length;
    this.cartItems = this.cartItems.filter(item => item.userId !== userId);
    return this.cartItems.length < initialLength;
  }

  // User dashboard preferences
  async getUserDasbarPreferences(userId: number): Promise<any> {
    return this.userDasbarPreferencesCache[userId] || null;
  }

  async saveUserDasbarPreferences(userId: number, items: any[]): Promise<any> {
    this.userDasbarPreferencesCache[userId] = { userId, items };
    return { userId, items };
  }

  // User history operations
  async getUserPurchaseHistory(userId: number, limit?: number): Promise<PurchaseHistoryItem[]> {
    const history = this.purchaseHistory.filter(item => item.userId === userId);
    return limit ? history.slice(0, limit) : history;
  }

  async getUserSearchHistory(userId: number, limit?: number): Promise<SearchHistoryItem[]> {
    const history = this.searchHistory.filter(item => item.userId === userId);
    return limit ? history.slice(0, limit) : history;
  }

  // Purchase operations
  async createPurchase(purchase: any): Promise<any> {
    const newPurchase = {
      ...purchase,
      id: this.purchases.length + 1,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.purchases.push(newPurchase);
    return newPurchase;
  }

  async getPurchaseById(purchaseId: number): Promise<any | undefined> {
    return this.purchases.find(p => p.id === purchaseId);
  }

  async getUserPurchases(userId: number): Promise<any[]> {
    return this.purchases
      .filter(p => p.userId === userId)
      .sort((a, b) => b.createdAt - a.createdAt);
  }

  async markPurchaseAsReceived(purchaseId: number): Promise<void> {
    const purchase = await this.getPurchaseById(purchaseId);
    if (purchase) {
      purchase.status = 'delivered';
      purchase.updatedAt = new Date();
    }
  }

  async submitPurchaseRating(purchaseId: number, rating: number, comment?: string): Promise<any> {
    const purchase = await this.getPurchaseById(purchaseId);
    if (purchase) {
      purchase.rating = rating;
      purchase.ratingComment = comment;
      purchase.updatedAt = new Date();

      // In a real implementation, we would update the seller's trust score here
      // For now, we'll just return the updated purchase
      return purchase;
    }
    throw new Error('Purchase not found');
  }

  async getPurchasesBySellerId(sellerId: number): Promise<any[]> {
    return this.purchases
      .filter(p => p.sellerId === sellerId)
      .sort((a, b) => b.createdAt - a.createdAt);
  }

  // SafeSphere operations
  async getSafeSphereStatus(userId: number): Promise<{ active: boolean }> {
    const user = await this.getUser(userId);
    return {
      active: user?.safeSphereActive || false
    };
  }

  async updateSafeSphereStatus(userId: number, active: boolean): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    await this.updateUser(userId, { safeSphereActive: active });
    return true;
  }

  // Seller operations
  async updateUserSellerStatus(userId: number, isSeller: boolean): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    await this.updateUser(userId, { isSeller });
    return true;
  }

  // ATOMIC INVENTORY OPERATIONS - Prevent overselling with database-level locking
  async reserveProductInventory(productId: number, requestedQuantity: number, userId: number): Promise<{
    success: boolean;
    error?: string;
    product?: Product;
    available?: number;
  }> {
    try {
      // Use database transaction with row-level locking to prevent race conditions
      const result = await db.transaction(async (tx) => {
        // Lock the product row for update (prevents concurrent modifications)
        const [product] = await tx
          .select()
          .from(products)
          .where(eq(products.id, productId))
          .for('update'); // This creates a row-level lock

        if (!product) {
          throw new Error('Product not found');
        }

        // Check if product is already sold
        if (product.status === 'sold') {
          throw new Error('This product is already sold');
        }

        // Calculate available quantity
        const availableQuantity = product.quantity - (product.soldQuantity || 0);

        if (availableQuantity < requestedQuantity) {
          throw new Error(`Insufficient quantity available. Available: ${availableQuantity}, Requested: ${requestedQuantity}`);
        }

        // Reserve the inventory by updating sold quantity
        const newSoldQuantity = (product.soldQuantity || 0) + requestedQuantity;
        const newStatus = newSoldQuantity >= product.quantity ? 'sold' : 'active';

        const [updatedProduct] = await tx
          .update(products)
          .set({
            soldQuantity: newSoldQuantity,
            status: newStatus,
            updatedAt: new Date()
          })
          .where(eq(products.id, productId))
          .returning();

        console.log(`🔒 ATOMIC RESERVATION: Product ${productId} - Reserved ${requestedQuantity} units for user ${userId}. New sold quantity: ${newSoldQuantity}/${product.quantity}, Status: ${newStatus}`);

        return {
          success: true,
          product: updatedProduct,
          available: availableQuantity
        };
      });

      return result;
    } catch (error: any) {
      console.error(`❌ RESERVATION FAILED: Product ${productId}, User ${userId}, Quantity ${requestedQuantity}:`, error.message);

      return {
        success: false,
        error: error.message,
        available: 0
      };
    }
  }

  async releaseProductInventory(productId: number, quantityToRelease: number): Promise<boolean> {
    try {
      // Release reserved inventory (rollback operation)
      const result = await db.transaction(async (tx) => {
        const [product] = await tx
          .select()
          .from(products)
          .where(eq(products.id, productId))
          .for('update');

        if (!product) {
          throw new Error('Product not found');
        }

        const newSoldQuantity = Math.max(0, (product.soldQuantity || 0) - quantityToRelease);
        const newStatus = newSoldQuantity >= product.quantity ? 'sold' : 'active';

        await tx
          .update(products)
          .set({
            soldQuantity: newSoldQuantity,
            status: newStatus,
            updatedAt: new Date()
          })
          .where(eq(products.id, productId));

        console.log(`🔓 INVENTORY RELEASED: Product ${productId} - Released ${quantityToRelease} units. New sold quantity: ${newSoldQuantity}/${product.quantity}, Status: ${newStatus}`);
        return true;
      });

      return result;
    } catch (error) {
      console.error(`❌ RELEASE FAILED: Product ${productId}, Quantity ${quantityToRelease}:`, error);
      return false;
    }
  }

  // Product operations
  async updateProductStatus(productId: number, status: string, soldQuantityToAdd: number = 0): Promise<Product | undefined> {
    try {
      // First try database update
      const [updatedProduct] = await db.update(products)
        .set({
          status,
          soldQuantity: sql`${products.soldQuantity} + ${soldQuantityToAdd}`,
          updatedAt: new Date()
        })
        .where(eq(products.id, productId))
        .returning();

      if (updatedProduct) {
        console.log(`✅ Database: Updated product ${productId} status to ${status}, added ${soldQuantityToAdd} to sold quantity`);
        return updatedProduct;
      }
    } catch (error) {
      console.error('Database error updating product status, falling back to memory:', error);
    }

    // Fallback to memory storage
    const product = this.products.find(p => p.id === productId);
    if (!product) return undefined;

    const updatedProduct = {
      ...product,
      status,
      soldQuantity: (product.soldQuantity || 0) + soldQuantityToAdd,
      updatedAt: new Date()
    };

    const index = this.products.findIndex(p => p.id === productId);
    if (index !== -1) {
      this.products[index] = updatedProduct;
    }

    console.log(`📝 Memory: Updated product ${productId} status to ${status}, sold quantity: ${updatedProduct.soldQuantity}`);
    return updatedProduct;
  }

  async getProducts(sphere: string, query?: string, categories?: string[]): Promise<Product[]> {
    console.log(`🔍 FallbackStorage.getProducts: sphere=${sphere}, query=${query || 'none'}, total products=${this.products.length}`);

    // Use a type assertion to handle the custom 'sphere' property
    let filteredProducts = this.products.filter(product => {
      const productWithSphere = product as unknown as { sphere: string };
      const matches = productWithSphere.sphere === sphere || sphere === 'all';
      console.log(`🔍 Product "${product.title}" sphere="${productWithSphere.sphere}" matches sphere "${sphere}": ${matches}`);
      return matches;
    });

    console.log(`🔍 After sphere filter: ${filteredProducts.length} products`);

    if (query) {
      const searchTerm = query.trim().toLowerCase();
      console.log(`🔍 FallbackStorage: Implementing smart word search for: "${searchTerm}"`);

      // Split search term into individual words
      const searchWords = searchTerm.split(/\s+/).filter(word => word.length > 0);
      console.log(`🔍 FallbackStorage: Search words: ${searchWords.join(', ')}`);

      filteredProducts = filteredProducts.filter(product => {
        const title = product.title.toLowerCase();
        const description = product.description.toLowerCase();

        if (searchWords.length === 1) {
          // Single word - use exact word matching
          const word = searchWords[0];
          const wordBoundaryRegex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);

          const titleMatch = wordBoundaryRegex.test(title) || title === word;
          const descriptionMatch = wordBoundaryRegex.test(description);
          const tagMatch = product.tags && product.tags.some(tag => tag.toLowerCase() === word);

          const matches = titleMatch || descriptionMatch || tagMatch;
          console.log(`🔍 Product "${product.title}" matches "${word}": title=${titleMatch}, desc=${descriptionMatch}, tag=${tagMatch}, overall=${matches}`);
          return matches;
        } else {
          // Multiple words - all words must be present (AND logic)
          const allWordsMatch = searchWords.every(word => {
            const wordBoundaryRegex = new RegExp(`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
            const titleMatch = wordBoundaryRegex.test(title);
            const descriptionMatch = wordBoundaryRegex.test(description);
            const tagMatch = product.tags && product.tags.some(tag => tag.toLowerCase() === word);

            return titleMatch || descriptionMatch || tagMatch;
          });

          console.log(`🔍 Product "${product.title}" matches all words "${searchWords.join(' ')}": ${allWordsMatch}`);
          return allWordsMatch;
        }
      });
    }

    // Filter by categories if provided
    if (categories && categories.length > 0) {
      filteredProducts = filteredProducts.filter(product => {
        // Check if product has a category that matches any of the provided categories
        if (product.categoryId) {
          // In fallback storage, we don't have actual category IDs, so we'll use the ai_attributes
          const aiAttributes = product.aiAttributes as any;
          if (aiAttributes && aiAttributes.category) {
            return categories.some(category =>
              aiAttributes.category.toLowerCase() === category.toLowerCase()
            );
          }
        }
        return false;
      });
    }

    console.log(`🔍 Final result: ${filteredProducts.length} products`);
    return filteredProducts;
  }

  async getProductById(id: number): Promise<Product | undefined> {
    return this.products.find(product => product.id === id);
  }

  async getProductsByCategory(categoryName: string): Promise<Product[]> {
    return this.getProducts('all', '', [categoryName]);
  }

  async createProduct(productData: any): Promise<Product> {
    const newProduct = {
      ...productData,
      id: this.products.length + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      price: typeof productData.price === 'string' ? parseFloat(productData.price) : productData.price,
      tags: Array.isArray(productData.tags) ? productData.tags : (productData.tags || '').split(',').map((tag: string) => tag.trim()),
      aiAttributes: productData.aiAttributes || {},
      searchVector: '',
    } as Product;

    this.products.push(newProduct);
    return newProduct;
  }

  async getProductsBySellerId(sellerId: number): Promise<Product[]> {
    return this.products.filter(product => product.sellerId === sellerId);
  }

  // Information content operations
  async getInformationContent(query?: string, category?: string): Promise<InformationContent[]> {
    let filteredContent = [...this.informationContent];

    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredContent = filteredContent.filter(content =>
        content.title.toLowerCase().includes(lowerQuery) ||
        content.content.toLowerCase().includes(lowerQuery)
      );
    }

    if (category) {
      filteredContent = filteredContent.filter(content =>
        content.category === category
      );
    }

    return filteredContent;
  }

  async getInformationContentById(id: number): Promise<InformationContent | undefined> {
    return this.informationContent.find(content => content.id === id);
  }

  // User session operations
  async createUserSession(session: any): Promise<any> {
    return {
      id: 1,
      userId: session.userId,
      sessionToken: session.sessionToken,
      deviceInfo: session.deviceInfo,
      createdAt: new Date(),
      expiresAt: session.expiresAt,
      isActive: true
    };
  }

  async getUserSessions(userId: number | null): Promise<any[]> {
    return [];
  }

  async deactivateSession(sessionToken: string): Promise<boolean> {
    // In fallback storage, just return success
    return true;
  }

  async deactivateAllUserSessions(userId: number): Promise<boolean> {
    // In fallback storage, just return success
    return true;
  }

  // App settings operations
  async getAppSettings(key: string): Promise<any> {
    return {};
  }

  async setAppSettings(key: string, value: any): Promise<boolean> {
    return true;
  }

  async getAllAppSettings(): Promise<{[key: string]: any}> {
    return {};
  }



  // AI operations
  async generateAiRecommendations(
    userId: number | null,
    preferredCategories?: string[],
    minPrice?: number,
    maxPrice?: number,
    useRandomMode?: boolean
  ): Promise<any[]> {
    // In fallback storage, return empty array
    return [];
  }

  async getAiRecommendations(userId: number | null): Promise<any[]> {
    // In fallback storage, return empty array
    return [];
  }

  // User history operations
  async addUserPurchaseHistory(
    userId: number,
    productId: number,
    categoryId: number | null,
    price: number,
    quantity?: number
  ): Promise<any> {
    // In fallback storage, return mock data
    return {
      id: 1,
      userId,
      productId,
      categoryId,
      price,
      quantity: quantity || 1,
      purchaseDate: new Date()
    };
  }

  async addUserSearchHistory(
    userId: number,
    searchQuery: string,
    categoryId?: number | null,
    clickedProductId?: number | null
  ): Promise<any> {
    // In fallback storage, return mock data
    return {
      id: 1,
      userId,
      searchQuery,
      categoryId: categoryId || null,
      clickedProductId: clickedProductId || null,
      searchDate: new Date()
    };
  }

  async getUserProductPreferences(userId: number): Promise<any[]> {
    // In fallback storage, return empty array
    return [];
  }

  async updateUserProductPreference(userId: number, categoryId: number, preferenceScore: number): Promise<any> {
    // In fallback storage, return mock data
    return {
      id: 1,
      userId,
      categoryId,
      preferenceScore,
      lastUpdated: new Date()
    };
  }

  // AutoShop operations
  private autoShopStatus: Map<string, any> = new Map();
  private autoShopSettings: Map<string, any> = new Map();
  private autoShopPendingItems: Map<string, any[]> = new Map();
  private autoShopOrderHistory: Map<string, any[]> = new Map();

  async getAutoShopStatus(userId: string | number): Promise<any> {
    const key = userId.toString();
    return this.autoShopStatus.get(key) || { active: false };
  }

  async setAutoShopStatus(userId: string | number, status: any): Promise<boolean> {
    const key = userId.toString();
    this.autoShopStatus.set(key, status);
    return true;
  }

  async getAutoShopSettings(userId: string | number): Promise<any> {
    const key = userId.toString();
    return this.autoShopSettings.get(key) || null;
  }

  async setAutoShopSettings(userId: string | number, settings: any): Promise<boolean> {
    const key = userId.toString();
    this.autoShopSettings.set(key, settings);
    return true;
  }

  async getAutoShopPendingItems(userId: string | number): Promise<any[]> {
    const key = userId.toString();
    return this.autoShopPendingItems.get(key) || [];
  }

  async addAutoShopItem(userId: string | number, item: any): Promise<boolean> {
    const key = userId.toString();
    const items = this.autoShopPendingItems.get(key) || [];
    items.push(item);
    this.autoShopPendingItems.set(key, items);
    return true;
  }

  async removeAutoShopItem(userId: string | number, itemId: string): Promise<boolean> {
    const key = userId.toString();
    const items = this.autoShopPendingItems.get(key) || [];
    const updatedItems = items.filter(item => item.id !== itemId);
    this.autoShopPendingItems.set(key, updatedItems);
    return true;
  }

  async clearAutoShopItems(userId: string | number): Promise<boolean> {
    const key = userId.toString();
    this.autoShopPendingItems.set(key, []);
    return true;
  }

  async getAutoShopOrderHistory(userId: string | number): Promise<any[]> {
    const key = userId.toString();
    return this.autoShopOrderHistory.get(key) || [];
  }

  async addAutoShopOrderHistoryItem(userId: string | number, item: any): Promise<boolean> {
    const key = userId.toString();
    const items = this.autoShopOrderHistory.get(key) || [];
    items.push(item);
    this.autoShopOrderHistory.set(key, items);
    return true;
  }

  // Order operations
  async createOrder(order: any): Promise<any> {
    return {
      id: 1,
      ...order,
      orderDate: new Date(),
      status: 'pending'
    };
  }

  async getOrderById(id: number): Promise<any> {
    return undefined;
  }

  async getOrdersByUserId(userId: number): Promise<any[]> {
    return [];
  }

  async updateOrderStatus(id: number, status: string): Promise<any> {
    return { id, status };
  }

  async addOrderItem(orderItem: any): Promise<any> {
    return { id: 1, ...orderItem };
  }

  async getOrderItemsByOrderId(orderId: number): Promise<any[]> {
    return [];
  }
}

// Create an instance of the fallback storage to use when needed
export const memoryStorage = new FallbackStorage();

// Define the streamlined storage interface with essential methods
export interface IStorage {
  // Session store
  sessionStore: session.Store;

  // Database connectivity test
  testConnection(): Promise<void>;

  // User related operations
  getUser(id: number): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>; // Alias for getUser for better readability
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;

  // User session operations
  createUserSession(session: any): Promise<any>;
  getUserSessions(userId: number | null): Promise<any[]>;
  deactivateSession(sessionToken: string): Promise<boolean>;
  deactivateAllUserSessions(userId: number): Promise<boolean>;

  // App settings operations
  getAppSettings(key: string): Promise<any>;
  setAppSettings(key: string, value: any): Promise<boolean>;
  getAllAppSettings(): Promise<{[key: string]: any}>;

  // User subscription operations
  updateUserSubscription(userId: number, subscriptionType: string, durationMonths: number): Promise<User>;
  checkUserHasSubscription(userId: number): Promise<boolean>;
  getUserSubscriptionDetails(userId: number): Promise<{hasSubscription: boolean, type?: string, expiresAt?: Date}>;

  // Product related operations
  getProducts(sphere: string, query?: string, categories?: string[]): Promise<Product[]>;
  getProductById(id: number): Promise<Product | undefined>;
  getProductsByCategory(categoryName: string): Promise<Product[]>;
  createProduct(product: any): Promise<Product>;
  getProductsBySellerId(sellerId: number): Promise<Product[]>;

  // Information content operations
  getInformationContent(query?: string, category?: string): Promise<InformationContent[]>;
  getInformationContentById(id: number): Promise<InformationContent | undefined>;

  // AI operations
  generateAiRecommendations(
    userId: number | null,
    preferredCategories?: string[],
    minPrice?: number,
    maxPrice?: number,
    useRandomMode?: boolean
  ): Promise<any[]>;

  getAiRecommendations(userId: number | null): Promise<any[]>;

  // User history operations
  addUserPurchaseHistory(userId: number, productId: number, categoryId: number | null, price: number, quantity?: number): Promise<any>;
  getUserPurchaseHistory(userId: number, limit?: number): Promise<any[]>;

  addUserSearchHistory(userId: number, searchQuery: string, categoryId?: number | null, clickedProductId?: number | null): Promise<any>;
  getUserSearchHistory(userId: number, limit?: number): Promise<any[]>;

  getUserProductPreferences(userId: number): Promise<any[]>;
  updateUserProductPreference(userId: number, categoryId: number, preferenceScore: number): Promise<any>;

  // AutoShop operations
  getAutoShopStatus(userId: string | number): Promise<any>;
  setAutoShopStatus(userId: string | number, status: any): Promise<boolean>;
  getAutoShopSettings(userId: string | number): Promise<any>;
  setAutoShopSettings(userId: string | number, settings: any): Promise<boolean>;
  getAutoShopPendingItems(userId: string | number): Promise<any[]>;
  addAutoShopItem(userId: string | number, item: any): Promise<boolean>;
  removeAutoShopItem(userId: string | number, itemId: string): Promise<boolean>;
  clearAutoShopItems(userId: string | number): Promise<boolean>;
  getAutoShopOrderHistory(userId: string | number): Promise<any[]>;
  addAutoShopOrderHistoryItem(userId: string | number, item: any): Promise<boolean>;

  // Order operations
  createOrder(order: any): Promise<any>;
  getOrderById(id: number): Promise<any>;
  getOrdersByUserId(userId: number): Promise<any[]>;
  updateOrderStatus(id: number, status: string): Promise<any>;
  addOrderItem(orderItem: any): Promise<any>;
  getOrderItemsByOrderId(orderId: number): Promise<any[]>;
}

// Database storage implementation
export class DatabaseStorage implements IStorage {
  public sessionStore: session.Store;

  constructor() {
    // Initialize a memory store
    const MemoryStore = memorystore(session);
    const memStore = new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    });

    // In production, we require a PostgreSQL session store
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL is required in production for session storage');
    }

    // Temporarily use memory store to avoid PostgreSQL connection issues
    log('Using memory session store (PostgreSQL temporarily disabled)', 'warning');
    this.sessionStore = memStore;
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  // Alias for getUser for better readability/consistency
  async getUserById(id: number): Promise<User | undefined> {
    return this.getUser(id);
  }

  // Test database connectivity for transaction safety
  async testConnection(): Promise<void> {
    try {
      // Simple query to test database connectivity
      await db.execute(sql`SELECT 1 as test`);
    } catch (error) {
      throw new Error(`Database connectivity test failed: ${error.message}`);
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    // Use case-insensitive exact match
    const [user] = await db.select().from(users).where(sql`LOWER(${users.username}) = LOWER(${username})`);
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    // Use case-insensitive exact match
    const [user] = await db.select().from(users).where(sql`LOWER(${users.email}) = LOWER(${email})`);
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Generate DasWos wallet credentials for the new user
    const walletCredentials = generateDasWosWalletCredentials();

    // Add the wallet ID to the user data
    const userWithWallet = {
      ...insertUser,
      walletId: walletCredentials.walletId
    };

    const [user] = await db.insert(users).values(userWithWallet).returning();

    // Create the wallet entry in the wallets database (without password initially)
    try {
      await this.createWalletEntry(walletCredentials.walletId, user.id, user.username);
    } catch (error) {
      // Don't fail user creation if wallet entry creation fails
    }

    // Log the wallet credentials for the user (in production, this should be sent securely to the user)
    console.log(`🎯 NEW USER WALLET CREDENTIALS:
    User: ${user.username} (ID: ${user.id})
    Wallet ID: ${walletCredentials.walletId}
    Wallet Password: ${walletCredentials.walletPassword}
    ⚠️  IMPORTANT: User must save these credentials to access their DasWos coins!`);

    return user;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    try {
      const [user] = await db
        .update(users)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
        .returning();

      return user;
    } catch (error) {
      console.error('Error updating user:', error);
      return undefined;
    }
  }

  async updateUserSubscription(userId: number, subscriptionType: string, durationMonths: number): Promise<User> {
    // Calculate the expiration date based on duration
    const now = new Date();
    const expiresAt = new Date(now);
    expiresAt.setMonth(now.getMonth() + durationMonths);

    // Set isFamilyOwner flag for family or unlimited subscriptions
    const isFamilyOwner = subscriptionType === 'family' || subscriptionType === 'unlimited';

    console.log(`Updating subscription for user ID=${userId}: type=${subscriptionType}, isFamilyOwner=${isFamilyOwner}`);

    const [user] = await db
      .update(users)
      .set({
        hasSubscription: true,
        subscriptionType: subscriptionType,
        subscriptionExpiresAt: expiresAt,
        isFamilyOwner: isFamilyOwner
      })
      .where(eq(users.id, userId))
      .returning();

    console.log(`User subscription updated: ID=${user.id}, type=${user.subscriptionType}, isFamilyOwner=${user.isFamilyOwner}`);

    return user;
  }

  // Stripe subscription operations
  async createStripeSubscription(
    userId: number,
    stripeCustomerId: string,
    stripeSubscriptionId: string,
    subscriptionType: string,
    billingCycle: string
  ): Promise<UserSubscription> {
    console.log(`Creating Stripe subscription for user ${userId} with billing cycle ${billingCycle}`);

    // First, check if a subscription already exists for this user
    const existingSubscription = await this.getStripeSubscription(userId);

    if (existingSubscription) {
      console.log(`Updating existing subscription for user ${userId}`);
      // Update the existing subscription
      const [subscription] = await db
        .update(userSubscriptions)
        .set({
          stripeCustomerId,
          stripeSubscriptionId,
          subscriptionType,
          billingCycle, // Ensure billing cycle is updated
          status: 'active',
          updatedAt: new Date()
        })
        .where(eq(userSubscriptions.userId, userId))
        .returning();

      return subscription;
    }

    console.log(`Creating new subscription for user ${userId} with billing cycle ${billingCycle}`);
    // Create a new subscription record
    const [subscription] = await db
      .insert(userSubscriptions)
      .values({
        userId,
        stripeCustomerId,
        stripeSubscriptionId,
        subscriptionType,
        billingCycle, // Ensure billing cycle is set
        status: 'active',
        currentPeriodStart: new Date(),
        // For monthly, add 1 month; for annual, add 1 year
        currentPeriodEnd: billingCycle === 'monthly'
          ? new Date(new Date().setMonth(new Date().getMonth() + 1))
          : new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        cancelAtPeriodEnd: false,
        metadata: {}
      })
      .returning();

    // Also update the user record
    await this.updateUserSubscription(
      userId,
      subscriptionType,
      billingCycle === 'monthly' ? 1 : 12
    );

    return subscription;
  }

  async updateStripeSubscription(
    userId: number,
    stripeSubscriptionId: string,
    status: string
  ): Promise<UserSubscription | undefined> {
    // Find the subscription
    const [subscription] = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.userId, userId),
          eq(userSubscriptions.stripeSubscriptionId, stripeSubscriptionId)
        )
      );

    if (!subscription) {
      return undefined;
    }

    // Update the subscription status
    const [updatedSubscription] = await db
      .update(userSubscriptions)
      .set({
        status,
        updatedAt: new Date(),
        // If canceled, set canceledAt
        ...(status === 'canceled' ? { canceledAt: new Date() } : {})
      })
      .where(eq(userSubscriptions.id, subscription.id))
      .returning();

    // If subscription is canceled, update the user record
    if (status === 'canceled') {
      await db
        .update(users)
        .set({
          hasSubscription: false,
          subscriptionType: 'limited', // Downgrade to free tier
          subscriptionExpiresAt: subscription.currentPeriodEnd // Will expire at the end of the current period
        })
        .where(eq(users.id, userId));
    }

    return updatedSubscription;
  }

  async getStripeSubscription(userId: number): Promise<UserSubscription | undefined> {
    const [subscription] = await db
      .select()
      .from(userSubscriptions)
      .where(eq(userSubscriptions.userId, userId));

    return subscription;
  }

  async checkUserHasSubscription(userId: number): Promise<boolean> {
    const [user] = await db
      .select({
        hasSubscription: users.hasSubscription,
        subscriptionExpiresAt: users.subscriptionExpiresAt,
        email: users.email
      })
      .from(users)
      .where(eq(users.id, userId));

    if (!user) return false;

    // Admin exception - always has access
    if (user.email === '<EMAIL>') return true;

    // Check subscription status and expiration
    if (!user.hasSubscription) return false;

    // Check if subscription is expired
    if (user.subscriptionExpiresAt && new Date() > user.subscriptionExpiresAt) {
      // Subscription expired, update status
      await db
        .update(users)
        .set({ hasSubscription: false })
        .where(eq(users.id, userId));
      return false;
    }

    return true;
  }

  async getUserSubscriptionDetails(userId: number): Promise<{hasSubscription: boolean, type?: string, expiresAt?: Date, billingCycle?: string}> {
    const [user] = await db
      .select({
        hasSubscription: users.hasSubscription,
        subscriptionType: users.subscriptionType,
        subscriptionExpiresAt: users.subscriptionExpiresAt,
        email: users.email
      })
      .from(users)
      .where(eq(users.id, userId));

    if (!user) return { hasSubscription: false };

    // Admin exception - always has access
    if (user.email === '<EMAIL>') {
      return {
        hasSubscription: true,
        type: 'admin',
        expiresAt: new Date('2099-12-31'),
        billingCycle: 'annual'
      };
    }

    // Check if subscription is expired
    if (user.subscriptionExpiresAt && new Date() > user.subscriptionExpiresAt) {
      // Subscription expired, update status
      await db
        .update(users)
        .set({ hasSubscription: false })
        .where(eq(users.id, userId));
      return { hasSubscription: false };
    }

    // Get billing cycle from user_subscriptions table
    let billingCycle: string | undefined;
    if (user.hasSubscription) {
      const [subscription] = await db
        .select({
          billingCycle: userSubscriptions.billingCycle
        })
        .from(userSubscriptions)
        .where(eq(userSubscriptions.userId, userId));

      if (subscription) {
        billingCycle = subscription.billingCycle;
      } else {
        // Default to monthly if no billing cycle is found
        billingCycle = 'monthly';

        // Log this situation for debugging
        console.log(`No billing cycle found for user ${userId} with subscription type ${user.subscriptionType}. Defaulting to monthly.`);
      }
    }

    return {
      hasSubscription: user.hasSubscription,
      type: user.subscriptionType || undefined,
      expiresAt: user.subscriptionExpiresAt || undefined,
      billingCycle: billingCycle
    };
  }

  /**
   * Update a user's seller status in the database
   * @param userId The ID of the user to update
   * @param isSeller Whether the user should be marked as a seller
   * @returns Success boolean
   */
  async updateUserSellerStatus(userId: number, isSeller: boolean): Promise<boolean> {
    try {
      // Skip updates for hardcoded admin user
      if (userId === 999999) {
        log(`Skipping seller status update for admin user ID ${userId}`, 'info');
        return true;
      }

      // Execute raw SQL to ensure we're using the correct field name
      // The users table in the database uses is_seller (snake_case), but our schema uses isSeller (camelCase)
      await db.execute(
        sql`UPDATE users SET is_seller = ${isSeller} WHERE id = ${userId}`
      );

      log(`Updated seller status for user ID ${userId} to ${isSeller} in database storage`, 'info');
      return true;
    } catch (error) {
      log(`Error updating user seller status: ${error}`, 'error');
      return false;
    }
  }

  // Product operations
  async getProducts(sphere: string, query?: string, categories?: string[]): Promise<Product[]> {
    console.log(`getProducts called with sphere=${sphere}, query=${query || 'none'}, categories=${categories ? categories.join(',') : 'none'}`);

    let conditions = [];

    // Handle different sphere combinations
    if (sphere === 'safesphere') {
      // Standard SafeSphere - for now just use trust score 70+ (relaxed for testing)
      // TODO: Re-enable identity verification when sellers have proper verification status
      // conditions.push(eq(products.identityVerified, true));
      // conditions.push(sql`${products.identityVerificationStatus} LIKE 'app%'`); // 'approved' or 'app*'
      conditions.push(gte(products.trustScore, 70));
      console.log('Added SafeSphere conditions: trustScore>=70 (identity verification temporarily disabled)');
    } else if (sphere === 'bulkbuy-safe') {
      // BulkBuy with SafeSphere filter - bulk buy eligible AND identity verified sellers
      conditions.push(eq(products.isBulkBuy, true));
      conditions.push(eq(products.identityVerified, true));
      conditions.push(sql`${products.identityVerificationStatus} LIKE 'app%'`); // 'approved' or 'app*'
      conditions.push(gte(products.trustScore, 70));
      console.log('Added BulkBuy-Safe conditions: isBulkBuy=true, identityVerified=true, identityVerificationStatus=app*, trustScore>=70');
    } else if (sphere === 'bulkbuy-open') {
      // BulkBuy with OpenSphere filter - just bulk buy eligible, any seller
      conditions.push(eq(products.isBulkBuy, true));
      console.log('Added BulkBuy-Open conditions: isBulkBuy=true');
    } else if (sphere === 'bulkbuy') {
      // Original BulkBuy sphere, used for backward compatibility
      conditions.push(eq(products.isBulkBuy, true));
      console.log('Added BulkBuy conditions: isBulkBuy=true');
    }

    // Filter by search query with smart word matching
    if (query && query.trim() !== '') {
      const searchTerm = query.trim().toLowerCase();
      console.log(`🔍 Implementing smart word search for: "${searchTerm}"`);

      // Split search term into individual words
      const searchWords = searchTerm.split(/\s+/).filter(word => word.length > 0);
      console.log(`🔍 Search words: ${searchWords.join(', ')}`);

      if (searchWords.length === 1) {
        // Single word - use exact word matching
        const word = searchWords[0];
        conditions.push(
          or(
            // Exact word match in title (using word boundaries)
            sql`LOWER(${products.title}) ~ ${`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`}`,
            // Exact word match in description (using word boundaries)
            sql`LOWER(${products.description}) ~ ${`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`}`,
            // Exact match in tags array
            sql`${word} = ANY(${products.tags})`,
            // Exact title match (case insensitive)
            sql`LOWER(${products.title}) = ${word}`
          )
        );
      } else {
        // Multiple words - all words must be present (AND logic)
        const wordConditions = searchWords.map(word =>
          or(
            sql`LOWER(${products.title}) ~ ${`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`}`,
            sql`LOWER(${products.description}) ~ ${`\\b${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`}`,
            sql`${word} = ANY(${products.tags})`
          )
        );

        // All words must match (AND logic)
        conditions.push(and(...wordConditions));
      }

      console.log(`🔍 Added smart word search conditions for: "${searchTerm}"`);
    }

    // Filter by categories if provided
    if (categories && categories.length > 0) {
      console.log(`Filtering by categories: ${categories.join(', ')}`);

      // Get category IDs for the provided category names
      const categoryIds = await this.getCategoryIdsByNames(categories);
      console.log(`Found category IDs: ${categoryIds.join(', ')}`);

      if (categoryIds.length > 0) {
        conditions.push(
          or(
            ...categoryIds.map(id => eq(products.categoryId, id))
          )
        );
        console.log(`Added category filter conditions for IDs: ${categoryIds.join(', ')}`);
      } else {
        console.log('WARNING: No matching category IDs found in the database');
      }
    }

    // Build the query with all conditions
    try {
      let result;
      if (conditions.length > 0) {
        console.log(`Executing query with ${conditions.length} conditions`);
        result = await db.select().from(products).where(and(...conditions)).limit(1000);
      } else {
        // If no conditions, return all products (with a reasonable limit)
        console.log('Executing query with no conditions (returning all products with limit)');
        result = await db.select().from(products).limit(1000);
      }

      console.log(`Query returned ${result.length} products`);

      // Log a sample of the products
      if (result.length > 0) {
        console.log('Sample product:', {
          id: result[0].id,
          title: result[0].title,
          price: result[0].price,
          categoryId: result[0].categoryId,
          sellerVerified: result[0].sellerVerified,
          trustScore: result[0].trustScore
        });
      }

      return result;
    } catch (error) {
      console.error('Error executing product query:', error);
      throw error; // Re-throw the error so HybridStorage can catch it and use fallback
    }
  }

  async getProductById(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product;
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const [product] = await db.insert(products).values(insertProduct).returning();
    return product;
  }

  async getProductsBySellerId(sellerId: number): Promise<Product[]> {
    return await db.select().from(products).where(eq(products.sellerId, sellerId));
  }

  async updateProductStatus(productId: number, status: string, soldQuantity?: number): Promise<Product | undefined> {
    try {
      const updateData: any = { status, updatedAt: new Date() };

      if (soldQuantity !== undefined) {
        updateData.soldQuantity = soldQuantity;
      }

      const [product] = await db
        .update(products)
        .set(updateData)
        .where(eq(products.id, productId))
        .returning();

      return product;
    } catch (error) {
      console.error('Error updating product status:', error);
      return undefined;
    }
  }

  // ATOMIC INVENTORY OPERATIONS for DatabaseStorage
  async reserveProductInventory(productId: number, requestedQuantity: number, userId: number): Promise<{
    success: boolean;
    error?: string;
    product?: Product;
    available?: number;
  }> {
    try {
      // Use database transaction with row-level locking to prevent race conditions
      const result = await db.transaction(async (tx: any) => {
        // Lock the product row for update (prevents concurrent modifications)
        const [product] = await tx
          .select()
          .from(products)
          .where(eq(products.id, productId))
          .for('update'); // This creates a row-level lock

        if (!product) {
          throw new Error('Product not found');
        }

        // Check if product is already sold
        if (product.status === 'sold') {
          throw new Error('This product is already sold');
        }

        // Calculate available quantity
        const availableQuantity = product.quantity - (product.soldQuantity || 0);

        if (availableQuantity < requestedQuantity) {
          throw new Error(`Insufficient quantity available. Available: ${availableQuantity}, Requested: ${requestedQuantity}`);
        }

        // Reserve the inventory by updating sold quantity
        const newSoldQuantity = (product.soldQuantity || 0) + requestedQuantity;
        const newStatus = newSoldQuantity >= product.quantity ? 'sold' : 'active';

        const [updatedProduct] = await tx
          .update(products)
          .set({
            soldQuantity: newSoldQuantity,
            status: newStatus,
            updatedAt: new Date()
          })
          .where(eq(products.id, productId))
          .returning();

        console.log(`🔒 DB ATOMIC RESERVATION: Product ${productId} - Reserved ${requestedQuantity} units for user ${userId}. New sold quantity: ${newSoldQuantity}/${product.quantity}, Status: ${newStatus}`);

        return {
          success: true,
          product: updatedProduct,
          available: availableQuantity
        };
      });

      return result;
    } catch (error: any) {
      console.error(`❌ DB RESERVATION FAILED: Product ${productId}, User ${userId}, Quantity ${requestedQuantity}:`, error.message);

      return {
        success: false,
        error: error.message,
        available: 0
      };
    }
  }

  async releaseProductInventory(productId: number, quantityToRelease: number): Promise<boolean> {
    try {
      // Release reserved inventory (rollback operation)
      const result = await db.transaction(async (tx: any) => {
        const [product] = await tx
          .select()
          .from(products)
          .where(eq(products.id, productId))
          .for('update');

        if (!product) {
          throw new Error('Product not found');
        }

        const newSoldQuantity = Math.max(0, (product.soldQuantity || 0) - quantityToRelease);
        const newStatus = newSoldQuantity >= product.quantity ? 'sold' : 'active';

        await tx
          .update(products)
          .set({
            soldQuantity: newSoldQuantity,
            status: newStatus,
            updatedAt: new Date()
          })
          .where(eq(products.id, productId));

        console.log(`🔓 DB INVENTORY RELEASED: Product ${productId} - Released ${quantityToRelease} units. New sold quantity: ${newSoldQuantity}/${product.quantity}, Status: ${newStatus}`);
        return true;
      });

      return result;
    } catch (error) {
      console.error(`❌ DB RELEASE FAILED: Product ${productId}, Quantity ${quantityToRelease}:`, error);
      return false;
    }
  }

  // Get category IDs by names
  async getCategoryIdsByNames(categoryNames: string[]): Promise<number[]> {
    console.log(`getCategoryIdsByNames called with: ${categoryNames ? categoryNames.join(', ') : 'none'}`);

    if (!categoryNames || categoryNames.length === 0) {
      console.log('No category names provided, returning empty array');
      return [];
    }

    try {
      // First, let's check what categories exist in the database
      const allCategories = await db.select().from(categories);
      console.log(`Database has ${allCategories.length} categories:`);
      allCategories.forEach(cat => {
        console.log(`- Category ID: ${cat.id}, Name: ${cat.name}`);
      });

      // Now query for the specific categories
      const categoryRows = await db
        .select({ id: categories.id, name: categories.name })
        .from(categories)
        .where(
          or(...categoryNames.map(name => eq(categories.name, name)))
        );

      console.log(`Found ${categoryRows.length} matching categories:`);
      categoryRows.forEach(cat => {
        console.log(`- Category ID: ${cat.id}, Name: ${cat.name}`);
      });

      return categoryRows.map(row => row.id);
    } catch (error) {
      console.error('Error getting category IDs by names:', error);
      return [];
    }
  }

  // Get products by category
  async getProductsByCategory(categoryName: string): Promise<Product[]> {
    try {
      // First get the category ID
      const [category] = await db
        .select()
        .from(categories)
        .where(eq(categories.name, categoryName));

      if (!category) {
        return [];
      }

      // Then get products with that category ID
      return await db
        .select()
        .from(products)
        .where(eq(products.categoryId, category.id));
    } catch (error) {
      console.error('Error getting products by category:', error);
      return [];
    }
  }

  // User history operations
  async addUserPurchaseHistory(
    userId: number,
    productId: number,
    categoryId: number | null,
    price: number,
    quantity: number = 1
  ): Promise<any> {
    try {
      const [record] = await db.insert(userPurchaseHistory).values({
        userId,
        productId,
        categoryId,
        price,
        quantity,
        purchaseDate: new Date()
      }).returning();

      return record;
    } catch (error) {
      console.error('Error adding user purchase history:', error);
      throw error;
    }
  }

  async getUserPurchaseHistory(userId: number, limit: number = 50): Promise<any[]> {
    try {
      const history = await db
        .select()
        .from(userPurchaseHistory)
        .where(eq(userPurchaseHistory.userId, userId))
        .orderBy(desc(userPurchaseHistory.purchaseDate))
        .limit(limit);

      return history;
    } catch (error) {
      console.error('Error getting user purchase history:', error);
      return [];
    }
  }

  async addUserSearchHistory(
    userId: number,
    searchQuery: string,
    categoryId: number | null = null,
    clickedProductId: number | null = null
  ): Promise<any> {
    try {
      const [record] = await db.insert(userSearchHistory).values({
        userId,
        searchQuery,
        categoryId,
        clickedProductId,
        searchDate: new Date()
      }).returning();

      return record;
    } catch (error) {
      console.error('Error adding user search history:', error);
      throw error;
    }
  }

  async getUserSearchHistory(userId: number, limit: number = 50): Promise<any[]> {
    try {
      const history = await db
        .select()
        .from(userSearchHistory)
        .where(eq(userSearchHistory.userId, userId))
        .orderBy(desc(userSearchHistory.searchDate))
        .limit(limit);

      return history;
    } catch (error) {
      console.error('Error getting user search history:', error);
      return [];
    }
  }

  async getUserProductPreferences(userId: number): Promise<any[]> {
    try {
      const preferences = await db
        .select({
          id: userProductPreferences.id,
          userId: userProductPreferences.userId,
          categoryId: userProductPreferences.categoryId,
          preferenceScore: userProductPreferences.preferenceScore,
          lastUpdated: userProductPreferences.lastUpdated,
          categoryName: categories.name
        })
        .from(userProductPreferences)
        .leftJoin(categories, eq(userProductPreferences.categoryId, categories.id))
        .where(eq(userProductPreferences.userId, userId))
        .orderBy(desc(userProductPreferences.preferenceScore));

      return preferences;
    } catch (error) {
      console.error('Error getting user product preferences:', error);
      return [];
    }
  }

  async updateUserProductPreference(userId: number, categoryId: number, preferenceScore: number): Promise<any> {
    try {
      // Check if preference already exists
      const [existingPreference] = await db
        .select()
        .from(userProductPreferences)
        .where(
          and(
            eq(userProductPreferences.userId, userId),
            eq(userProductPreferences.categoryId, categoryId)
          )
        );

      if (existingPreference) {
        // Update existing preference
        const [updatedPreference] = await db
          .update(userProductPreferences)
          .set({
            preferenceScore,
            lastUpdated: new Date()
          })
          .where(eq(userProductPreferences.id, existingPreference.id))
          .returning();

        return updatedPreference;
      } else {
        // Create new preference
        const [newPreference] = await db
          .insert(userProductPreferences)
          .values({
            userId,
            categoryId,
            preferenceScore,
            lastUpdated: new Date()
          })
          .returning();

        return newPreference;
      }
    } catch (error) {
      console.error('Error updating user product preference:', error);
      throw error;
    }
  }

  // Search operations
  async saveSearchQuery(insertSearchQuery: InsertSearchQuery): Promise<SearchQuery> {
    // Ensure contentType is set with a default value if not provided
    const searchQueryWithDefaults = {
      ...insertSearchQuery,
      contentType: insertSearchQuery.contentType || "products"
    };

    const [searchQuery] = await db.insert(searchQueries).values(searchQueryWithDefaults).returning();
    return searchQuery;
  }

  async getRecentSearches(limit: number): Promise<SearchQuery[]> {
    return await db.select().from(searchQueries).orderBy(desc(searchQueries.timestamp)).limit(limit);
  }

  // Seller verification operations
  async createSellerVerification(verification: InsertSellerVerification): Promise<SellerVerification> {
    const [sellerVerification] = await db.insert(sellerVerifications).values(verification).returning();
    return sellerVerification;
  }

  async getSellerVerificationsByUserId(userId: number): Promise<SellerVerification[]> {
    return await db.select().from(sellerVerifications).where(eq(sellerVerifications.userId, userId));
  }

  async updateSellerVerificationStatus(id: number, status: string, comments?: string): Promise<SellerVerification> {
    const data: any = {
      status,
      processedAt: new Date()
    };

    if (comments) {
      data.comments = comments;
    }

    const [sellerVerification] = await db
      .update(sellerVerifications)
      .set(data)
      .where(eq(sellerVerifications.id, id))
      .returning();

    return sellerVerification;
  }

  // New seller methods implementation
  async findSellerByUserId(userId: number): Promise<any | undefined> {
    try {
      const [seller] = await db.execute(
        sql`SELECT * FROM sellers WHERE user_id = ${userId} LIMIT 1`
      );
      return seller || undefined;
    } catch (error) {
      console.error("Error finding seller:", error);
      return undefined;
    }
  }

  async getSellerById(sellerId: number): Promise<any | undefined> {
    try {
      const [seller] = await db.execute(
        sql`SELECT * FROM sellers WHERE id = ${sellerId} LIMIT 1`
      );
      return seller || undefined;
    } catch (error) {
      console.error("Error finding seller by ID:", error);
      return undefined;
    }
  }

  async getPendingSellerVerifications(): Promise<any[]> {
    try {
      // Get all sellers with pending verification status
      const result = await db.execute(
        sql`SELECT s.*, u.email, u.username, u.full_name
            FROM sellers s
            JOIN users u ON s.user_id = u.id
            WHERE s.verification_status = 'pending'
            ORDER BY s.created_at DESC`
      );
      return result || [];
    } catch (error) {
      console.error("Error getting pending seller verifications:", error);
      return [];
    }
  }

  async getAllSellerVerifications(): Promise<any[]> {
    try {
      // Get all sellers with user information, sorted by status (pending first) and creation date
      const result = await db.execute(
        sql`SELECT
              s.id,
              s.user_id AS "userId",
              s.business_name AS "businessName",
              s.business_type AS "businessType",
              s.verification_status AS "verificationStatus",
              s.business_address AS "businessAddress",
              s.contact_phone AS "contactPhone",
              s.tax_id AS "taxId",
              s.website,
              s.year_established AS "yearEstablished",
              s.description,
              s.created_at AS "createdAt",
              s.updated_at AS "updatedAt",
              u.email,
              u.username,
              u.full_name AS "fullName"
            FROM sellers s
            JOIN users u ON s.user_id = u.id
            ORDER BY
              CASE
                WHEN s.verification_status = 'pending' THEN 1
                WHEN s.verification_status = 'rejected' THEN 2
                WHEN s.verification_status = 'approved' THEN 3
                ELSE 4
              END,
              s.created_at DESC`
      );
      console.log("Seller verifications retrieved:", result.length);
      return result || [];
    } catch (error) {
      console.error("Error getting all seller verifications:", error);
      return [];
    }
  }

  // Calculate seller trust score based on provided information
  calculateSellerTrustScore(data: any): number {
    let score = 50; // Base score for all sellers

    // Business verification adds more trust
    if (data.business_type && data.business_type !== 'individual') {
      score += 10; // Business sellers get a higher base score
    }

    // Add points for complete information
    if (data.business_name) score += 5;
    if (data.business_address) score += 5;
    if (data.contact_phone) score += 5;
    if (data.tax_id) score += 10;
    if (data.website) score += 5;
    if (data.year_established) {
      score += 5;

      // Older businesses get additional points (up to 10)
      const yearsInBusiness = new Date().getFullYear() - data.year_established;
      if (yearsInBusiness > 0) {
        score += Math.min(10, yearsInBusiness);
      }
    }
    if (data.description && data.description.length > 50) score += 5;

    // Boost for document verification
    if (data.document_urls && data.document_urls.length > 0) {
      score += 10 * Math.min(data.document_urls.length, 3); // Up to 30 points for 3+ documents
    }

    // Approved verification status is a significant boost
    if (data.verification_status === 'approved') {
      score += 20;
    }

    // Cap the score at 100
    return Math.min(100, score);
  }

  async createSeller(sellerData: any): Promise<any> {
    try {
      // Calculate trust score based on the provided information
      const trustScore = this.calculateSellerTrustScore(sellerData);

      const [seller] = await db.execute(
        sql`INSERT INTO sellers (
          user_id, business_name, business_type, verification_status, business_address,
          contact_phone, tax_id, year_established, website, description, created_at, trust_score
        ) VALUES (
          ${sellerData.user_id}, ${sellerData.business_name}, ${sellerData.business_type},
          ${sellerData.verification_status || 'pending'}, ${sellerData.business_address},
          ${sellerData.contact_phone}, ${sellerData.tax_id}, ${sellerData.year_established},
          ${sellerData.website}, ${sellerData.description}, ${sellerData.created_at || new Date()},
          ${trustScore}
        ) RETURNING *`
      );

      // If seller is approved, update the user's isSeller flag
      if (seller && seller.verification_status === 'approved' && seller.user_id) {
        // Execute raw SQL to ensure we're using the correct field name
        await db.execute(
          sql`UPDATE users SET is_seller = true WHERE id = ${seller.user_id}`
        );
      }

      return seller;
    } catch (error) {
      console.error("Error creating seller:", error);
      throw error;
    }
  }

  async updateSeller(sellerId: number, data: any): Promise<any> {
    try {
      // First, get the current seller data using raw SQL query to avoid importing the schema
      const getSellerQuery = `
        SELECT * FROM sellers WHERE id = $1
      `;
      const [currentSeller] = await db.execute(sql.raw(getSellerQuery, sellerId));

      if (!currentSeller) {
        throw new Error(`Seller with ID ${sellerId} not found`);
      }

      // Note: Trust score is now managed exclusively by the rating system
      // We no longer automatically recalculate trust scores when seller data is updated
      // Trust scores are only updated through submitPurchaseRating following SELLER_TRUST_SCORE.md

      const updateFields = [];
      const values = [];
      let paramIndex = 1;

      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      // Always add updated_at
      updateFields.push(`updated_at = $${paramIndex}`);
      values.push(new Date());

      const query = `
        UPDATE sellers
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex + 1}
        RETURNING *
      `;

      values.push(sellerId);

      // Create proper params array for the SQL query
      const [seller] = await db.execute(sql.raw(query, ...values));

      // If this update is changing verification status to approved, update the user's isSeller flag
      if (data.verification_status === 'approved' && currentSeller.user_id) {
        // Execute raw SQL to ensure we're using the correct field name
        await db.execute(
          sql`UPDATE users SET is_seller = true WHERE id = ${currentSeller.user_id}`
        );
      }

      return seller;
    } catch (error) {
      console.error("Error updating seller:", error);
      throw error;
    }
  }

  // Family account operations
  async addFamilyMember(ownerUserId: number, email: string): Promise<{ success: boolean, message: string }> {
    // First, check if the owner has a family subscription
    const owner = await this.getUser(ownerUserId);
    if (!owner || owner.subscriptionType !== 'family' || !owner.hasSubscription) {
      return { success: false, message: "You need a family subscription to add members" };
    }

    // Check if the owner is indeed marked as a family owner
    if (!owner.isFamilyOwner) {
      // Update the owner status first
      await db
        .update(users)
        .set({ isFamilyOwner: true })
        .where(eq(users.id, ownerUserId));
    }

    // Count existing family members
    const existingMembers = await this.getFamilyMembers(ownerUserId);
    if (existingMembers.length >= 4) {
      return { success: false, message: "Maximum of 4 family members allowed" };
    }

    // Check if the email exists
    const memberUser = await this.getUserByEmail(email);
    if (!memberUser) {
      return { success: false, message: "User with this email not found" };
    }

    // Check if the user is already part of a family
    if (memberUser.parentAccountId) {
      return { success: false, message: "User is already part of a family account" };
    }

    // Add the user to the family
    await db
      .update(users)
      .set({
        parentAccountId: ownerUserId,
        // Optionally inherit owner's SuperSafe settings
        superSafeMode: owner.superSafeMode,
        superSafeSettings: owner.superSafeSettings
      })
      .where(eq(users.id, memberUser.id));

    return { success: true, message: "Family member added successfully" };
  }

  async removeFamilyMember(memberUserId: number): Promise<boolean> {
    const member = await this.getUser(memberUserId);
    if (!member || !member.parentAccountId) {
      return false;
    }

    // Remove the member from the family
    await db
      .update(users)
      .set({
        parentAccountId: null,
        // Reset SuperSafe to defaults
        superSafeMode: false,
        superSafeSettings: {
          blockGambling: true,
          blockAdultContent: true,
          blockOpenSphere: false
        }
      })
      .where(eq(users.id, memberUserId));

    return true;
  }

  async getFamilyMembers(ownerUserId: number): Promise<User[]> {
    // Get all users where the parentAccountId is the owner's ID
    const result = await db
      .select()
      .from(users)
      .where(eq(users.parentAccountId, ownerUserId));

    // For debugging
    console.log(`Found ${result.length} family members for owner ID ${ownerUserId}`);

    return result;
  }

  async isFamilyOwner(userId: number): Promise<boolean> {
    const user = await this.getUser(userId);
    return !!(user && user.isFamilyOwner);
  }

  async createChildAccount(ownerUserId: number, childName: string): Promise<{ success: boolean, message: string, account?: { username: string, password: string } }> {
    console.log(`Creating child account for owner ID ${ownerUserId}, child name: ${childName}`);

    // First, check if the owner has a family subscription
    const owner = await this.getUser(ownerUserId);
    console.log(`Owner details: ID=${owner?.id}, isFamilyOwner=${owner?.isFamilyOwner}, subscriptionType=${owner?.subscriptionType}, hasSubscription=${owner?.hasSubscription}`);

    if (!owner || owner.subscriptionType !== 'family' || !owner.hasSubscription) {
      return { success: false, message: "You need a family subscription to create child accounts" };
    }

    // Check if the owner is indeed marked as a family owner
    if (!owner.isFamilyOwner) {
      // User has a family subscription but isFamilyOwner flag is not set
      // Let's fix that automatically
      console.log(`User ${ownerUserId} has family subscription but isFamilyOwner flag is false. Fixing...`);
      await db
        .update(users)
        .set({ isFamilyOwner: true })
        .where(eq(users.id, ownerUserId));

      // Update our local copy for the rest of this function
      owner.isFamilyOwner = true;
    }

    // Count existing family members
    const existingMembers = await this.getFamilyMembers(ownerUserId);
    console.log(`Found ${existingMembers.length} existing family members`);

    if (existingMembers.length >= 4) {
      return { success: false, message: "Maximum of 4 family members allowed" };
    }

    // Generate a fun username (base on child's name + random animal/color)
    const animals = ['Panda', 'Tiger', 'Lion', 'Zebra', 'Giraffe', 'Dolphin', 'Koala', 'Penguin', 'Fox', 'Wolf'];
    const colors = ['Red', 'Blue', 'Green', 'Purple', 'Orange', 'Yellow', 'Pink', 'Turquoise', 'Violet', 'Gold'];

    const randomAnimal = animals[Math.floor(Math.random() * animals.length)];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    const randomNumber = Math.floor(Math.random() * 100);

    // Convert childName to a safe format (lowercase, remove spaces)
    const safeName = childName.toLowerCase().replace(/\s+/g, '');
    const username = `${safeName}${randomAnimal}${randomNumber}`;

    // Generate a secure but memorable password
    const password = `${randomColor}${randomAnimal}${randomNumber}!`;

    // Generate a valid email address for the child account
    const childEmail = `${username}@family.${owner.email.split('@')[1]}`;

    // Create the child account
    try {
      const childUser = await this.createUser({
        username,
        email: childEmail,
        password, // Note: Will be hashed by the auth system
        fullName: childName,
        parentAccountId: ownerUserId,
        isChildAccount: true,
        superSafeMode: true, // Child accounts have SuperSafe mode enabled by default
        superSafeSettings: {
          blockGambling: true,
          blockAdultContent: true,
          blockOpenSphere: true
        }
      });

      console.log(`Child account created: ID=${childUser.id}, username=${childUser.username}, parentAccountId=${childUser.parentAccountId}, isChildAccount=${childUser.isChildAccount}`);

      return {
        success: true,
        message: "Child account created successfully",
        account: {
          username,
          password
        }
      };
    } catch (error) {
      return {
        success: false,
        message: "Failed to create child account. Please try again."
      };
    }
  }

  async updateChildAccountPassword(childUserId: number, newPassword: string): Promise<boolean> {
    const child = await this.getUser(childUserId);
    if (!child || !child.isChildAccount) {
      return false;
    }

    try {
      await db
        .update(users)
        .set({
          password: newPassword // Note: Will be hashed by the auth system when used
        })
        .where(eq(users.id, childUserId));

      return true;
    } catch (error) {
      return false;
    }
  }

  // SuperSafe mode operations
  async getSuperSafeStatus(userId: number): Promise<{enabled: boolean, settings: any}> {
    const user = await this.getUser(userId);
    if (!user) {
      return {
        enabled: false,
        settings: {
          blockGambling: true,
          blockAdultContent: true,
          blockOpenSphere: false
        }
      };
    }

    return {
      enabled: user.superSafeMode,
      settings: user.superSafeSettings || {
        blockGambling: true,
        blockAdultContent: true,
        blockOpenSphere: false
      }
    };
  }

  async updateSuperSafeStatus(userId: number, enabled: boolean, settings?: any): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    // If settings not provided, keep existing settings
    const updatedSettings = settings || user.superSafeSettings || {
      blockGambling: true,
      blockAdultContent: true,
      blockOpenSphere: false
    };

    await db
      .update(users)
      .set({
        superSafeMode: enabled,
        superSafeSettings: updatedSettings
      })
      .where(eq(users.id, userId));

    return true;
  }

  async updateFamilyMemberSuperSafeStatus(ownerUserId: number, memberUserId: number, enabled: boolean, settings?: any): Promise<boolean> {
    // First verify this is actually a family owner
    const isOwner = await this.isFamilyOwner(ownerUserId);
    if (!isOwner) return false;

    // Then verify the member is part of this family
    const member = await this.getUser(memberUserId);
    if (!member || member.parentAccountId !== ownerUserId) return false;

    // Update the member's settings
    return this.updateSuperSafeStatus(memberUserId, enabled, settings);
  }

  // SafeSphere operations
  async getSafeSphereStatus(userId: number): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    return user.safeSphereActive || false;
  }

  async updateSafeSphereStatus(userId: number, active: boolean): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    await db
      .update(users)
      .set({ safeSphereActive: active })
      .where(eq(users.id, userId));

    return true;
  }

  // Bulk Buy Agent operations
  async createBulkBuyRequest(request: InsertBulkBuyRequest): Promise<BulkBuyRequest> {
    const [bulkBuyRequest] = await db.insert(bulkBuyRequests).values(request).returning();
    return bulkBuyRequest;
  }

  async getBulkBuyRequestsByUserId(userId: number): Promise<BulkBuyRequest[]> {
    return await db.select().from(bulkBuyRequests).where(eq(bulkBuyRequests.userId, userId));
  }

  async getBulkBuyRequestById(id: number): Promise<BulkBuyRequest | undefined> {
    const [request] = await db.select().from(bulkBuyRequests).where(eq(bulkBuyRequests.id, id));
    return request;
  }

  async updateBulkBuyRequestStatus(id: number, status: string, agentId?: number): Promise<BulkBuyRequest> {
    const data: any = {
      status,
      updatedAt: new Date()
    };

    if (agentId) {
      data.assignedAgentId = agentId;
    }

    const [request] = await db
      .update(bulkBuyRequests)
      .set(data)
      .where(eq(bulkBuyRequests.id, id))
      .returning();

    return request;
  }

  // AI Shopper operations
  async getAiShopperStatus(userId: number): Promise<{enabled: boolean, settings: any}> {
    const [user] = await db
      .select({
        aiShopperEnabled: users.aiShopperEnabled,
        aiShopperSettings: users.aiShopperSettings
      })
      .from(users)
      .where(eq(users.id, userId));

    if (!user) {
      return {
        enabled: false,
        settings: {
          autoPurchase: false,
          budgetLimit: 5000, // in cents ($50)
          preferredCategories: [],
          avoidTags: [],
          minimumTrustScore: 85
        }
      };
    }

    return {
      enabled: user.aiShopperEnabled,
      settings: user.aiShopperSettings
    };
  }

  async updateAiShopperStatus(userId: number, enabled: boolean, settings?: any): Promise<boolean> {
    const data: any = {
      aiShopperEnabled: enabled
    };

    if (settings) {
      data.aiShopperSettings = settings;
    }

    try {
      await db
        .update(users)
        .set(data)
        .where(eq(users.id, userId));
      return true;
    } catch (error) {
      console.error('Failed to update AI Shopper status:', error);
      return false;
    }
  }

  async createAiShopperRecommendation(recommendation: InsertAiShopperRecommendation): Promise<AiShopperRecommendation> {
    const [result] = await db
      .insert(aiShopperRecommendations)
      .values({
        ...recommendation,
        createdAt: new Date()
      })
      .returning();

    return result;
  }

  async getRecommendationById(userId: number | null, recommendationId: number): Promise<AiShopperRecommendation | null> {
    try {
      // Build the query
      let query = db
        .select()
        .from(aiShopperRecommendations)
        .where(eq(aiShopperRecommendations.id, recommendationId));

      // If userId is provided, also filter by userId
      if (userId !== null) {
        query = query.where(eq(aiShopperRecommendations.userId, userId));
      }

      // Execute the query
      const recommendations = await query;

      if (recommendations.length === 0) {
        return null;
      }

      // Get recommendation
      const recommendation = recommendations[0];

      // Check if it's permanently removed
      if (recommendation.rejectedReason && recommendation.rejectedReason.includes('[PERMANENT_REMOVAL]')) {
        return null;
      }

      // Get associated product
      const [product] = await db
        .select()
        .from(products)
        .where(eq(products.id, recommendation.productId));

      // Return recommendation with product info
      return {
        ...recommendation,
        product
      };
    } catch (error) {
      console.error("Error getting recommendation by ID:", error);
      throw error;
    }
  }

  async getAiShopperRecommendationsByUserId(userId: number): Promise<AiShopperRecommendation[]> {
    // Get all recommendations for the user
    const recommendations = await db
      .select()
      .from(aiShopperRecommendations)
      .where(eq(aiShopperRecommendations.userId, userId))
      .orderBy(desc(aiShopperRecommendations.createdAt));

    // Filter out permanently removed items (those marked with [PERMANENT_REMOVAL])
    return recommendations.filter(rec => {
      if (rec.rejectedReason && rec.rejectedReason.includes('[PERMANENT_REMOVAL]')) {
        console.log(`Filtering out permanently removed recommendation ${rec.id}`);
        return false;
      }
      return true;
    });
  }

  async clearAiShopperRecommendations(userId: number): Promise<void> {
    console.log(`Clearing all AI Shopper recommendations for user ID ${userId}`);

    try {
      // Delete all recommendations for the user
      await db
        .delete(aiShopperRecommendations)
        .where(eq(aiShopperRecommendations.userId, userId));

      console.log(`Successfully cleared recommendations for user ID ${userId}`);
    } catch (error) {
      console.error(`Error clearing recommendations for user ID ${userId}:`, error);
      throw error;
    }
  }

  async updateAiShopperRecommendationStatus(id: number, status: string, reason?: string, removeFromList?: boolean): Promise<AiShopperRecommendation> {
    // If removeFromList is true and status is rejected, we'll handle it differently
    if (removeFromList === true && status === 'rejected') {
      console.log(`Permanently removing recommendation ${id} from list`);

      // For permanently removing the item, we'll simply update its status
      // but mark it specially in the rejectedReason to know it should be filtered out
      const data: any = {
        status,
        updatedAt: new Date(),
        rejectedReason: reason ? `${reason} [PERMANENT_REMOVAL]` : 'Permanently removed by user'
      };

      const [recommendation] = await db
        .update(aiShopperRecommendations)
        .set(data)
        .where(eq(aiShopperRecommendations.id, id))
        .returning();

      return recommendation;
    }

    // Standard update case
    const data: any = {
      status,
      updatedAt: new Date()
    };

    if (status === 'purchased') {
      data.purchasedAt = new Date();
    }

    if (status === 'rejected' && reason) {
      data.rejectedReason = reason;
    }

    const [recommendation] = await db
      .update(aiShopperRecommendations)
      .set(data)
      .where(eq(aiShopperRecommendations.id, id))
      .returning();

    return recommendation;
  }

  async getAiRecommendations(userId: number | null): Promise<any[]> {
    try {
      if (userId === null) {
        // For anonymous users, return empty array
        return [];
      }

      // Get recommendations for the user
      const recommendations = await db
        .select({
          id: aiShopperRecommendations.id,
          userId: aiShopperRecommendations.userId,
          productId: aiShopperRecommendations.productId,
          reason: aiShopperRecommendations.reason,
          confidence: aiShopperRecommendations.confidence,
          status: aiShopperRecommendations.status,
          createdAt: aiShopperRecommendations.createdAt,
          updatedAt: aiShopperRecommendations.updatedAt
        })
        .from(aiShopperRecommendations)
        .where(eq(aiShopperRecommendations.userId, userId))
        .orderBy(desc(aiShopperRecommendations.createdAt));

      // Get product details for each recommendation
      const recommendationsWithProducts = await Promise.all(
        recommendations.map(async (rec) => {
          const product = await this.getProductById(rec.productId);
          return {
            ...rec,
            product
          };
        })
      );

      return recommendationsWithProducts.filter(rec => rec.product !== undefined);
    } catch (error) {
      console.error('Error getting AI recommendations:', error);
      return [];
    }
  }

  async generateAiRecommendations(
    userId: number | null,
    preferredCategories: string[] = [],
    minPrice: number = 0,
    maxPrice: number = 100000,
    useRandomMode: boolean = false,
    useSafeSphere: boolean = false
  ): Promise<any[]> {
    try {
      console.log(`Generating AI recommendations for user ${userId || 'anonymous'}`);
      console.log(`Parameters: categories=${preferredCategories.join(',')}, price range=${minPrice}-${maxPrice}, random=${useRandomMode}, safeSphere=${useSafeSphere}`);

      // Step 1: Get products based on categories and price range
      let sphere = useSafeSphere ? 'safesphere' : 'opensphere'; // Use SafeSphere only when explicitly requested
      let potentialProducts;

      // If categories are specified, try to get products by category
      if (preferredCategories && preferredCategories.length > 0) {
        console.log(`Trying to get products by categories: ${preferredCategories.join(', ')}`);

        // Get all products first
        potentialProducts = await this.getProducts(sphere);
        console.log(`Found ${potentialProducts.length} total products in SafeSphere`);

        // Check if categories exist in the database
        const categoryIds = await this.getCategoryIdsByNames(preferredCategories);
        console.log(`Found ${categoryIds.length} matching category IDs: ${categoryIds.join(', ')}`);

        if (categoryIds.length > 0) {
          // Filter products by category IDs
          potentialProducts = potentialProducts.filter(product =>
            categoryIds.includes(product.categoryId)
          );
          console.log(`After category filtering: ${potentialProducts.length} products`);
        } else {
          // If no matching categories, log a warning but continue with all products
          console.log('WARNING: No matching categories found in database. Using all products.');
        }
      } else {
        // No categories specified, get all products
        potentialProducts = await this.getProducts(sphere);
        console.log(`No categories specified. Found ${potentialProducts.length} total products in SafeSphere`);
      }

      // Filter by price range
      potentialProducts = potentialProducts.filter(product =>
        product.price >= minPrice && product.price <= maxPrice
      );

      console.log(`After price filtering: ${potentialProducts.length} potential products matching criteria`);

      if (potentialProducts.length === 0) {
        console.log('No products found matching criteria');
        return [];
      }

      // Step 2: If user is logged in, use their history to personalize recommendations
      let selectedProducts = [];

      if (userId !== null && !useRandomMode) {
        // Get user's purchase history
        const purchaseHistory = await this.getUserPurchaseHistory(userId);

        // Get user's search history
        const searchHistory = await this.getUserSearchHistory(userId);

        // Get user's product preferences
        const productPreferences = await this.getUserProductPreferences(userId);

        if (purchaseHistory.length > 0 || searchHistory.length > 0 || productPreferences.length > 0) {
          console.log('Using user history for personalized recommendations');

          // Create a scoring system for products based on user history
          const productScores = new Map<number, number>();

          // Initialize scores for all potential products
          potentialProducts.forEach(product => {
            productScores.set(product.id, 0);
          });

          // Score based on purchase history
          purchaseHistory.forEach(purchase => {
            // Boost products in the same category
            potentialProducts.forEach(product => {
              if (product.categoryId === purchase.categoryId) {
                const currentScore = productScores.get(product.id) || 0;
                productScores.set(product.id, currentScore + 5);
              }
            });
          });

          // Score based on search history
          searchHistory.forEach(search => {
            // Boost products that match search terms
            const searchTerms = search.searchQuery.toLowerCase().split(' ');
            potentialProducts.forEach(product => {
              const titleWords = product.title.toLowerCase().split(' ');
              const descriptionWords = product.description.toLowerCase().split(' ');

              // Check for matches in title and description
              searchTerms.forEach(term => {
                if (titleWords.some(word => word.includes(term))) {
                  const currentScore = productScores.get(product.id) || 0;
                  productScores.set(product.id, currentScore + 3);
                }

                if (descriptionWords.some(word => word.includes(term))) {
                  const currentScore = productScores.get(product.id) || 0;
                  productScores.set(product.id, currentScore + 1);
                }
              });

              // Boost products in the same category as clicked products
              if (search.clickedProductId && search.categoryId && product.categoryId === search.categoryId) {
                const currentScore = productScores.get(product.id) || 0;
                productScores.set(product.id, currentScore + 4);
              }
            });
          });

          // Score based on product preferences
          productPreferences.forEach(preference => {
            potentialProducts.forEach(product => {
              if (product.categoryId === preference.categoryId) {
                const currentScore = productScores.get(product.id) || 0;
                productScores.set(product.id, currentScore + (preference.preferenceScore * 2));
              }
            });
          });

          // Sort products by score
          const scoredProducts = potentialProducts.map(product => ({
            product,
            score: productScores.get(product.id) || 0
          }));

          scoredProducts.sort((a, b) => b.score - a.score);

          // Select top 5 products
          selectedProducts = scoredProducts.slice(0, 5).map(item => item.product);

          console.log(`Selected ${selectedProducts.length} products based on user history`);
        } else {
          console.log('No user history found, using random selection');
          // Fall back to random selection if no history
          selectedProducts = this.getRandomProducts(potentialProducts, 5);
        }
      } else {
        console.log('Using random selection mode');
        // For anonymous users or random mode, select random products
        selectedProducts = this.getRandomProducts(potentialProducts, 5);
      }

      // Step 3: Create recommendations for the selected products
      const recommendations = [];

      for (const product of selectedProducts) {
        try {
          // Create a recommendation
          const [recommendation] = await db.insert(aiShopperRecommendations).values({
            userId: userId || 1, // Use default user ID 1 for anonymous users
            productId: product.id,
            reason: `This product matches your preferences in the ${product.categoryId ? await this.getCategoryName(product.categoryId) : 'selected'} category.`,
            confidence: 85, // Default confidence
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
          }).returning();

          // Add product details to the recommendation
          recommendations.push({
            ...recommendation,
            product
          });
        } catch (error) {
          console.error(`Error creating recommendation for product ${product.id}:`, error);
        }
      }

      return recommendations;
    } catch (error) {
      console.error('Error in generateAiRecommendations:', error);
      return [];
    }
  }

  // Helper method to get random products
  private getRandomProducts(products: Product[], count: number): Product[] {
    const shuffled = [...products].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  // Helper method to get category name by ID
  private async getCategoryName(categoryId: number): Promise<string> {
    try {
      const [category] = await db
        .select({ name: categories.name })
        .from(categories)
        .where(eq(categories.id, categoryId));

      return category?.name || 'Unknown';
    } catch (error) {
      console.error(`Error getting category name for ID ${categoryId}:`, error);
      return 'Unknown';
    }
  }

  async processAutoPurchase(recommendationId: number): Promise<{ success: boolean; message: string; }> {
    try {
      // Step 1: Get the recommendation
      const [recommendation] = await db
        .select()
        .from(aiShopperRecommendations)
        .where(eq(aiShopperRecommendations.id, recommendationId));

      if (!recommendation) {
        return { success: false, message: "Recommendation not found" };
      }

      // Step 2: Get the product
      const product = await this.getProductById(recommendation.productId);
      if (!product) {
        return { success: false, message: "Product not found" };
      }

      // Step 3: Get the user's AI Shopper settings
      // We're using settings directly without checking if it's enabled
      const { settings } = await this.getAiShopperStatus(recommendation.userId);

      // Step 4: Import the Anthropic validation function
      const { validateAutomatedPurchase } = await import('./anthropic');

      // Step 5: Validate the purchase
      const validationResult = validateAutomatedPurchase(
        settings,
        {
          confidence: recommendation.confidence / 100, // Convert from 0-100 to 0-1
          productId: recommendation.productId
        }
      );

      // If purchase validation fails, update recommendation status and return
      if (!validationResult.isValid) {
        await this.updateAiShopperRecommendationStatus(
          recommendationId,
          'rejected',
          validationResult.reasoning || 'Purchase validation failed'
        );

        return {
          success: false,
          message: validationResult.reasoning || 'Purchase validation failed'
        };
      }

      // Step 6: Check if user has chosen to use DasWos Coins
      if (settings.useCoins) {
        // Convert product price from cents to dollars for DasWos Coins (1 coin = $1)
        const priceInCoins = Math.ceil(product.price / 100);

        // Check if user has enough DasWos Coins
        const userCoins = await this.getUserDasWosCoins(recommendation.userId);
        if (userCoins < priceInCoins) {
          await this.updateAiShopperRecommendationStatus(
            recommendationId,
            'rejected',
            `Not enough DasWos Coins. Required: ${priceInCoins}, Available: ${userCoins}`
          );
          return {
            success: false,
            message: `Not enough DasWos Coins. Required: ${priceInCoins}, Available: ${userCoins}`
          };
        }

        // Spend DasWos Coins for the purchase (1 coin = $1)
        const coinSpent = await this.spendDasWosCoins(
          recommendation.userId,
          priceInCoins,
          `Auto-purchase: ${product.title}`,
          {
            productId: product.id,
            recommendationId: recommendation.id
          }
        );

        if (!coinSpent) {
          await this.updateAiShopperRecommendationStatus(
            recommendationId,
            'rejected',
            'Failed to process DasWos Coins transaction'
          );
          return {
            success: false,
            message: "Failed to process DasWos Coins transaction"
          };
        }

        // Add product to cart with "ai_shopper" source
        await this.addCartItem({
          userId: recommendation.userId,
          productId: product.id,
          quantity: 1,
          source: "ai_shopper" // Use "ai_shopper" for automated purchases with DasWos Coins
        });

        // Update recommendation status to purchased
        await this.updateAiShopperRecommendationStatus(recommendationId, 'purchased');

        return {
          success: true,
          message: `Auto-purchased ${product.title} for ${priceInCoins} DasWos Coins`
        };
      } else {
        // Step 7: Process with payment method if not using coins
        const paymentMethod = await this.getDefaultPaymentMethod(recommendation.userId);
        if (!paymentMethod) {
          await this.updateAiShopperRecommendationStatus(
            recommendationId,
            'rejected',
            'No default payment method found'
          );
          return {
            success: false,
            message: "No default payment method found"
          };
        }

        // Process the payment using Stripe
        try {
          // Import stripe service
          const { createPaymentIntent } = await import('./stripe');

          // Get user to fetch details
          const user = await this.getUser(recommendation.userId);
          if (!user) {
            throw new Error('User not found');
          }

          // In a production environment, we would create a real payment intent
          // and process the payment using the stored payment method
          console.log(`Processing payment for recommendation ${recommendationId}:`, {
            productId: product.id,
            price: product.price,
            paymentMethodId: paymentMethod.stripePaymentMethodId,
            userId: recommendation.userId,
            userEmail: user.email
          });

          // This would be used in a real implementation to process the payment
          // const paymentIntent = await createPaymentIntent(
          //   product.price,
          //   'gbp', // Currency
          //   user.email,
          //   paymentMethod.stripePaymentMethodId,
          //   `Product purchase: ${product.title}`,
          //   {
          //     recommendationId: recommendation.id.toString(),
          //     productId: product.id.toString(),
          //     automated: 'true'
          //   }
          // );

          // Step 8: Update recommendation status to purchased
          await this.updateAiShopperRecommendationStatus(recommendationId, 'purchased');

          // Record the purchase in the order history (would be implemented in a real system)
          // await this.createOrder({
          //   userId: recommendation.userId,
          //   productId: product.id,
          //   quantity: 1,
          //   totalPrice: product.price,
          //   paymentStatus: 'completed',
          //   paymentMethodId: paymentMethod.id,
          //   stripePaymentIntentId: paymentIntent.id,
          //   automatedPurchase: true,
          //   recommendationId: recommendation.id
          // });

          return {
            success: true,
            message: "Purchase completed successfully"
          };
        } catch (error) {
          // If payment processing fails, update the recommendation status
          await this.updateAiShopperRecommendationStatus(
            recommendationId,
            'rejected',
            `Payment processing failed: ${error instanceof Error ? error.message : String(error)}`
          );

          return {
            success: false,
            message: `Payment processing failed: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      }
    } catch (error) {
      console.error('Error in processAutoPurchase:', error);
      return {
        success: false,
        message: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }



  // Payment method operations
  async getUserPaymentMethods(userId: number): Promise<UserPaymentMethod[]> {
    return await db
      .select()
      .from(userPaymentMethods)
      .where(eq(userPaymentMethods.userId, userId))
      .orderBy(desc(userPaymentMethods.isDefault));
  }

  async getDefaultPaymentMethod(userId: number): Promise<UserPaymentMethod | undefined> {
    const [paymentMethod] = await db
      .select()
      .from(userPaymentMethods)
      .where(and(
        eq(userPaymentMethods.userId, userId),
        eq(userPaymentMethods.isDefault, true)
      ));

    return paymentMethod;
  }

  async addUserPaymentMethod(paymentMethod: InsertUserPaymentMethod): Promise<UserPaymentMethod> {
    // If this is the first payment method or isDefault is true,
    // ensure it's set as the default
    if (paymentMethod.isDefault) {
      // Set all other payment methods for this user to non-default
      await db
        .update(userPaymentMethods)
        .set({ isDefault: false })
        .where(eq(userPaymentMethods.userId, paymentMethod.userId));
    } else {
      // Check if the user has any payment methods
      const existingMethods = await this.getUserPaymentMethods(paymentMethod.userId);
      if (existingMethods.length === 0) {
        // If this is the first one, make it default regardless of what was passed
        paymentMethod.isDefault = true;
      }
    }

    const [result] = await db
      .insert(userPaymentMethods)
      .values(paymentMethod)
      .returning();

    return result;
  }

  async setDefaultPaymentMethod(paymentMethodId: number, userId: number): Promise<boolean> {
    // First, verify the payment method belongs to the user
    const [paymentMethod] = await db
      .select()
      .from(userPaymentMethods)
      .where(and(
        eq(userPaymentMethods.id, paymentMethodId),
        eq(userPaymentMethods.userId, userId)
      ));

    if (!paymentMethod) {
      return false;
    }

    // Set all payment methods for this user to non-default
    await db
      .update(userPaymentMethods)
      .set({ isDefault: false })
      .where(eq(userPaymentMethods.userId, userId));

    // Set the selected payment method as default
    await db
      .update(userPaymentMethods)
      .set({ isDefault: true })
      .where(eq(userPaymentMethods.id, paymentMethodId));

    return true;
  }

  async deletePaymentMethod(paymentMethodId: number): Promise<boolean> {
    // Get the payment method first to check if it's the default
    const [paymentMethod] = await db
      .select()
      .from(userPaymentMethods)
      .where(eq(userPaymentMethods.id, paymentMethodId));

    if (!paymentMethod) {
      return false;
    }

    // Delete the payment method
    await db
      .delete(userPaymentMethods)
      .where(eq(userPaymentMethods.id, paymentMethodId));

    // If it was the default, make another one the default if available
    if (paymentMethod.isDefault) {
      const [nextPaymentMethod] = await db
        .select()
        .from(userPaymentMethods)
        .where(eq(userPaymentMethods.userId, paymentMethod.userId))
        .limit(1);

      if (nextPaymentMethod) {
        await db
          .update(userPaymentMethods)
          .set({ isDefault: true })
          .where(eq(userPaymentMethods.id, nextPaymentMethod.id));
      }
    }

    return true;
  }

  // App settings operations
  async getAppSettings(key: string): Promise<any> {
    const [setting] = await db
      .select()
      .from(appSettings)
      .where(eq(appSettings.key, key));

    return setting ? setting.value : null;
  }

  async setAppSettings(key: string, value: any): Promise<boolean> {
    try {
      // Check if setting already exists
      const existing = await this.getAppSettings(key);

      if (existing) {
        // Update existing setting
        await db
          .update(appSettings)
          .set({
            value,
            updatedAt: new Date()
          })
          .where(eq(appSettings.key, key));
      } else {
        // Create new setting
        await db
          .insert(appSettings)
          .values({
            key,
            value,
            createdAt: new Date()
          });
      }

      return true;
    } catch (error) {
      console.error(`Error setting app setting ${key}:`, error);
      return false;
    }
  }

  // User Dasbar Preferences operations
  async getUserDasbarPreferences(userId: number): Promise<UserDasbarPreferences | null> {
    const [preferences] = await db
      .select()
      .from(userDasbarPreferences)
      .where(eq(userDasbarPreferences.userId, userId));

    return preferences || null;
  }

  async saveUserDasbarPreferences(userId: number, items: any[]): Promise<UserDasbarPreferences> {
    // Check if preferences already exist for this user
    const existingPreferences = await this.getUserDasbarPreferences(userId);

    if (existingPreferences) {
      // Update existing preferences
      const [updatedPreferences] = await db
        .update(userDasbarPreferences)
        .set({
          items,
          updatedAt: new Date()
        })
        .where(eq(userDasbarPreferences.userId, userId))
        .returning();

      return updatedPreferences;
    } else {
      // Create new preferences
      const [newPreferences] = await db
        .insert(userDasbarPreferences)
        .values({
          userId,
          items,
          createdAt: new Date()
        })
        .returning();

      return newPreferences;
    }
  }

  async getAllAppSettings(): Promise<{[key: string]: any}> {
    try {
      const settings = await db
        .select()
        .from(appSettings);

      // Convert to a key/value map
      return settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as {[key: string]: any});
    } catch (error) {
      console.error('Error in getAllAppSettings:', error);
      throw error; // Re-throw to trigger fallback
    }
  }

  // Information content operations
  async getInformationContent(query?: string, category?: string): Promise<InformationContent[]> {
    let conditions = [];

    // Filter by category if provided
    if (category) {
      conditions.push(eq(informationContent.category, category));
    }

    // Filter by search query
    if (query && query.trim() !== '') {
      conditions.push(
        or(
          like(informationContent.title, `%${query}%`),
          like(informationContent.content, `%${query}%`),
          like(informationContent.summary, `%${query}%`)
        )
      );
    }

    // Build the query with all conditions
    if (conditions.length > 0) {
      return await db.select().from(informationContent).where(and(...conditions)).orderBy(desc(informationContent.trustScore));
    }

    // No conditions means return all information content, ordered by trust score
    return await db.select().from(informationContent).orderBy(desc(informationContent.trustScore));
  }

  async getInformationContentById(id: number): Promise<InformationContent | undefined> {
    const [content] = await db.select().from(informationContent).where(eq(informationContent.id, id));
    return content;
  }

  async createInformationContent(content: InsertInformationContent): Promise<InformationContent> {
    // Ensure all required fields have default values if not provided
    const contentWithDefaults = {
      ...content,
      sourceVerified: content.sourceVerified ?? false,
      sourceType: content.sourceType || "website",
      imageUrl: content.imageUrl || null,
      verifiedSince: content.verifiedSince || null,
      warning: content.warning || null
    };

    const [newContent] = await db.insert(informationContent).values(contentWithDefaults).returning();
    return newContent;
  }

  async getInformationContentByCategory(category: string): Promise<InformationContent[]> {
    return await db.select().from(informationContent)
      .where(eq(informationContent.category, category))
      .orderBy(desc(informationContent.trustScore));
  }

  // Collaborative Search operations
  async createCollaborativeSearch(search: InsertCollaborativeSearch): Promise<CollaborativeSearch> {
    const [collaborativeSearch] = await db.insert(collaborativeSearches).values(search).returning();

    // Automatically add the creator as a collaborator with owner role
    await this.addCollaborator({
      searchId: collaborativeSearch.id,
      userId: collaborativeSearch.userId,
      role: "owner",
      status: "active"
    });

    return collaborativeSearch;
  }

  async getCollaborativeSearchById(id: number): Promise<CollaborativeSearch | undefined> {
    const [search] = await db.select().from(collaborativeSearches).where(eq(collaborativeSearches.id, id));
    return search;
  }

  async getUserCollaborativeSearches(userId: number): Promise<CollaborativeSearch[]> {
    // Get searches where user is the creator
    return await db.select().from(collaborativeSearches)
      .where(eq(collaborativeSearches.userId, userId))
      .orderBy(desc(collaborativeSearches.createdAt));
  }

  async updateCollaborativeSearch(id: number, data: Partial<InsertCollaborativeSearch>): Promise<CollaborativeSearch> {
    // Set the updated timestamp
    const updateData = {
      ...data,
      updatedAt: new Date()
    };

    const [search] = await db.update(collaborativeSearches)
      .set(updateData)
      .where(eq(collaborativeSearches.id, id))
      .returning();

    return search;
  }

  async searchCollaborativeSearches(query: string, topic?: string): Promise<CollaborativeSearch[]> {
    // Build the SQL query directly for more control
    let sqlQuery = sql`
      SELECT * FROM ${collaborativeSearches}
      WHERE ${collaborativeSearches.isPublic} = true
      AND ${collaborativeSearches.status} = 'active'
    `;

    // Add topic filter if provided
    if (topic) {
      sqlQuery = sql`${sqlQuery} AND ${collaborativeSearches.topic} = ${topic}`;
    }

    // Add search query filter if provided
    if (query && query.trim() !== '') {
      const searchPattern = `%${query.trim()}%`;
      sqlQuery = sql`${sqlQuery}
        AND (
          ${collaborativeSearches.title} ILIKE ${searchPattern}
          OR ${collaborativeSearches.description} ILIKE ${searchPattern}
        )
      `;
    }

    // Add ordering and limit
    sqlQuery = sql`${sqlQuery}
      ORDER BY ${collaborativeSearches.createdAt} DESC
      LIMIT 20
    `;

    // Execute the query and return properly typed results
    return await db.execute(sqlQuery) as unknown as CollaborativeSearch[];
  }

  // Collaborative Resources operations
  async addResourceToCollaborativeSearch(resource: InsertCollaborativeResource): Promise<CollaborativeResource> {
    const [newResource] = await db.insert(collaborativeResources).values(resource).returning();
    return newResource;
  }

  async getResourceById(id: number): Promise<CollaborativeResource | undefined> {
    const [resource] = await db.select().from(collaborativeResources).where(eq(collaborativeResources.id, id));
    return resource;
  }

  async getResourcesBySearchId(searchId: number): Promise<CollaborativeResource[]> {
    return await db.select().from(collaborativeResources)
      .where(eq(collaborativeResources.searchId, searchId))
      .orderBy(desc(collaborativeResources.createdAt));
  }

  async updateResource(id: number, data: Partial<InsertCollaborativeResource>): Promise<CollaborativeResource> {
    // Set the updated timestamp
    const updateData = {
      ...data,
      updatedAt: new Date()
    };

    const [resource] = await db.update(collaborativeResources)
      .set(updateData)
      .where(eq(collaborativeResources.id, id))
      .returning();

    return resource;
  }

  // Collaborator operations
  async addCollaborator(collaborator: InsertCollaborativeCollaborator): Promise<CollaborativeCollaborator> {
    const [newCollaborator] = await db.insert(collaborativeCollaborators).values(collaborator).returning();
    return newCollaborator;
  }

  async getSearchCollaborators(searchId: number): Promise<CollaborativeCollaborator[]> {
    return await db.select().from(collaborativeCollaborators)
      .where(eq(collaborativeCollaborators.searchId, searchId));
  }

  async getUserCollaborations(userId: number): Promise<CollaborativeCollaborator[]> {
    return await db.select().from(collaborativeCollaborators)
      .where(
        and(
          eq(collaborativeCollaborators.userId, userId),
          eq(collaborativeCollaborators.status, "active")
        )
      );
  }

  async removeCollaborator(searchId: number, userId: number): Promise<boolean> {
    // Don't actually delete, just set status to removed
    const result = await db.update(collaborativeCollaborators)
      .set({ status: "removed" })
      .where(
        and(
          eq(collaborativeCollaborators.searchId, searchId),
          eq(collaborativeCollaborators.userId, userId)
        )
      );

    return true;
  }

  // Resource Permission operations
  async requestResourcePermission(request: InsertResourcePermissionRequest): Promise<ResourcePermissionRequest> {
    const [newRequest] = await db.insert(resourcePermissionRequests).values(request).returning();
    return newRequest;
  }

  async getResourcePermissionRequests(resourceId: number): Promise<ResourcePermissionRequest[]> {
    return await db.select().from(resourcePermissionRequests)
      .where(eq(resourcePermissionRequests.resourceId, resourceId))
      .orderBy(desc(resourcePermissionRequests.createdAt));
  }

  async getUserPermissionRequests(userId: number): Promise<ResourcePermissionRequest[]> {
    return await db.select().from(resourcePermissionRequests)
      .where(eq(resourcePermissionRequests.requesterId, userId))
      .orderBy(desc(resourcePermissionRequests.createdAt));
  }

  async updatePermissionRequestStatus(requestId: number, status: string): Promise<ResourcePermissionRequest> {
    // Set the updated timestamp
    const updateData = {
      status,
      updatedAt: new Date()
    };

    const [request] = await db.update(resourcePermissionRequests)
      .set(updateData)
      .where(eq(resourcePermissionRequests.id, requestId))
      .returning();

    return request;
  }

  // Shopping Cart operations
  async getUserCartItems(userId: number): Promise<CartItemWithProduct[]> {
    try {
      // Join cart items with products to get full product details
      const result = await db.select({
        cartItem: cartItems,
        product: products
      })
      .from(cartItems)
      .leftJoin(products, eq(cartItems.productId, products.id))
      .where(eq(cartItems.userId, userId))
      .orderBy(desc(cartItems.addedAt));

      // Map the joined results to CartItemWithProduct objects, handling null products
      return result.map(row => {
        const item: CartItemWithProduct = {
          ...row.cartItem,
          product: row.product || undefined
        };
        return item;
      });
    } catch (error) {
      console.error("Error fetching user cart items:", error);
      return [];
    }
  }

  async addCartItem(item: InsertCartItem): Promise<CartItem> {
    try {
      // Check if the item already exists in the cart
      const existingItems = await db.select()
        .from(cartItems)
        .where(
          and(
            eq(cartItems.userId, item.userId),
            eq(cartItems.productId, item.productId)
          )
        );

      if (existingItems.length > 0) {
        // Item already exists, update quantity
        const existingItem = existingItems[0];
        const newQuantity = existingItem.quantity + (item.quantity || 1);

        const [updatedItem] = await db.update(cartItems)
          .set({
            quantity: newQuantity,
            updatedAt: new Date(),
            ...(item.source ? { source: item.source } : {})
          })
          .where(eq(cartItems.id, existingItem.id))
          .returning();

        return updatedItem;
      } else {
        // Item doesn't exist, insert new
        const newItem = {
          ...item,
          quantity: item.quantity || 1,
          addedAt: new Date(),
          updatedAt: null,
          source: item.source || 'manual'
        };

        const [createdItem] = await db.insert(cartItems).values(newItem).returning();
        return createdItem;
      }
    } catch (error) {
      console.error("Error adding item to cart:", error);
      throw error;
    }
  }

  async updateCartItemQuantity(itemId: number, quantity: number): Promise<CartItem> {
    try {
      const [updatedItem] = await db.update(cartItems)
        .set({
          quantity,
          updatedAt: new Date()
        })
        .where(eq(cartItems.id, itemId))
        .returning();

      if (!updatedItem) {
        throw new Error("Cart item not found");
      }

      return updatedItem;
    } catch (error) {
      console.error("Error updating cart item quantity:", error);
      throw error;
    }
  }

  async removeCartItem(itemId: number): Promise<void> {
    try {
      await db.delete(cartItems)
        .where(eq(cartItems.id, itemId));
    } catch (error) {
      console.error("Error removing item from cart:", error);
      throw error;
    }
  }

  async clearUserCart(userId: number): Promise<void> {
    try {
      await db.delete(cartItems)
        .where(eq(cartItems.userId, userId));
    } catch (error) {
      console.error("Error clearing user cart:", error);
      throw error;
    }
  }

  async addAiRecommendationToCart(userId: number, recommendationId: number): Promise<CartItem> {
    try {
      // First, get the recommendation to find the product
      // Don't filter by userId to allow any user to add AI recommendations (supports demo mode)
      const recommendations = await db.select()
        .from(aiShopperRecommendations)
        .where(eq(aiShopperRecommendations.id, recommendationId));

      if (recommendations.length === 0) {
        // If no recommendations found, check if this is a free access scenario
        // Check for setting that might enable free access
        const paidFeaturesDisabled = await this.getAppSettings('paidFeaturesDisabled');

        if (!paidFeaturesDisabled) {
          // If not in free access mode, recommendation should be tied to user
          throw new Error("Recommendation not found");
        }

        // For free access mode, try again with the demo user ID (usually 1)
        const demoRecommendations = await db.select()
          .from(aiShopperRecommendations)
          .where(eq(aiShopperRecommendations.id, recommendationId));

        if (demoRecommendations.length === 0) {
          throw new Error("Recommendation not found in free access mode");
        }
      }

      const recommendation = recommendations[0];

      // Add the product to the cart with the current user's ID
      const cartItem = await this.addCartItem({
        userId, // Use the current user's ID regardless of recommendation.userId
        productId: recommendation.productId,
        quantity: 1,
        source: "ai_recommendation", // Use "ai_recommendation" for manually added AI recommendations
        recommendationId
      });

      // Update the recommendation status
      await db.update(aiShopperRecommendations)
        .set({
          status: "added_to_cart",
          updatedAt: new Date()
        })
        .where(eq(aiShopperRecommendations.id, recommendationId));

      return cartItem;
    } catch (error) {
      console.error("Error adding AI recommendation to cart:", error);
      throw error;
    }
  }

  // DasWos Coins operations (user-owned balance system)
  async getUserDasWosCoins(userId: number): Promise<number> {
    try {
      // Get user's balance from the users table
      const [user] = await db
        .select({ balance: users.dasWosCoinsBalance })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      return user.balance || 0;
    } catch (error) {
      console.error(`Error getting DasWos Coins balance for user ${userId}:`, error);
      return 0;
    }
  }

  // Check if user's wallet matches (for balance access control)
  async checkUserWalletAccess(userId: number, walletId?: string): Promise<{ hasAccess: boolean; userWalletId?: string; requiresWalletLink?: boolean }> {
    try {
      const [user] = await db
        .select({ walletId: users.walletId })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return { hasAccess: false };
      }

      // SECURITY FIX: If user has no linked wallet, they CANNOT access balance through any wallet
      if (!user.walletId) {
        return {
          hasAccess: false,
          userWalletId: null,
          requiresWalletLink: true
        };
      }

      // If wallet ID provided, check if it matches user's linked wallet (STRICT MATCH REQUIRED)
      if (walletId) {
        const matches = user.walletId === walletId;

        if (!matches) {
          return {
            hasAccess: false,
            userWalletId: user.walletId
          };
        }

        // CRITICAL SECURITY CHECK: Verify wallet exists in wallets database

        try {
          // Import Supabase client for wallet database
          const { createClient } = await import('@supabase/supabase-js');

          // Wallet database configuration
          const WALLET_SUPABASE_URL = 'https://mjyaqqsxhkqyzqufpxzl.supabase.co';
          const WALLET_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs';

          // Create Supabase client for wallet database
          const walletClient = createClient(WALLET_SUPABASE_URL, WALLET_SUPABASE_KEY);

          // Check if wallet exists in wallets table
          const { data: walletData, error: walletError } = await walletClient
            .from('wallets')
            .select('wallet_id, is_active')
            .eq('wallet_id', walletId)
            .single();

          if (walletError || !walletData) {
            return {
              hasAccess: false,
              userWalletId: user.walletId
            };
          }

          return {
            hasAccess: true,
            userWalletId: user.walletId
          };

        } catch (walletDbError) {
          return {
            hasAccess: false,
            userWalletId: user.walletId
          };
        }
      }

      // If no wallet ID provided but user has linked wallet, deny access (wallet connection required)
      return {
        hasAccess: false,
        userWalletId: user.walletId
      };
    } catch (error) {
      console.error(`Error checking wallet access for user ${userId}:`, error);
      return { hasAccess: false };
    }
  }

  // Create wallet entry in wallets database (without password initially)
  async createWalletEntry(walletId: string, userId: number, username: string): Promise<void> {
    try {

      // Import Supabase client for wallet database
      const { createClient } = await import('@supabase/supabase-js');

      // Wallet database configuration
      const WALLET_SUPABASE_URL = 'https://mjyaqqsxhkqyzqufpxzl.supabase.co';
      const WALLET_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs';

      // Create Supabase client for wallet database
      const walletClient = createClient(WALLET_SUPABASE_URL, WALLET_SUPABASE_KEY);

      // 1. Insert into wallets table (without password_hash initially)
      // Don't include password_hash field at all to avoid NOT NULL constraint
      const { data: walletData, error: walletError } = await walletClient
        .from('wallets')
        .insert({
          wallet_id: walletId,
          is_active: true, // Active immediately so user can see balance
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (walletError) {
        throw new Error(`Failed to create wallet entry: ${walletError.message}`);
      }

      // 2. Insert into wallet_connections table to link wallet to user
      const { data: connectionData, error: connectionError } = await walletClient
        .from('wallet_connections')
        .insert({
          wallet_id: walletId,
          user_id: userId,
          username: username,
          connected_at: new Date().toISOString(),
          is_active: true
        })
        .select()
        .single();

      if (connectionError) {
        // Don't throw here - wallet was created successfully, connection is secondary
      }

    } catch (error) {
      console.error(`❌ Failed to create wallet entry for ${walletId}:`, error);
      throw error;
    }
  }

  async addDasWosCoins(userId: number, amount: number, type: string, description: string, metadata?: any, walletId?: string): Promise<boolean> {
    try {
      // Validate that amount is positive for adding coins
      if (amount <= 0) {
        throw new Error('Amount must be positive for adding coins');
      }

      // Validate type is valid for adding coins
      const validTypes = ['purchase', 'reward', 'refund', 'admin'];
      if (!validTypes.includes(type)) {
        throw new Error(`Invalid type for adding coins: ${type}. Must be one of: ${validTypes.join(', ')}`);
      }

      // Use a transaction to ensure both operations succeed or fail together
      await db.transaction(async (tx) => {
        // Add transaction record
        await tx.insert(dasWosCoinsTransactions).values({
          userId,
          amount,
          transactionType: type,
          description,
          metadata: metadata ? JSON.stringify(metadata) : null,
          walletId: walletId || null, // Track which wallet was used
          createdAt: new Date()
        });

        // Update user's balance and link wallet if provided
        const updateData: any = {
          dasWosCoinsBalance: sql`${users.dasWosCoinsBalance} + ${amount}`
        };

        // If wallet ID is provided, link it to the user
        if (walletId) {
          updateData.walletId = walletId;
        }

        await tx
          .update(users)
          .set(updateData)
          .where(eq(users.id, userId));
      });

      return true;
    } catch (error) {
      console.error(`Error adding DasWos Coins for user ${userId}:`, error);
      return false;
    }
  }

  async spendDasWosCoins(userId: number, amount: number, description: string, metadata?: any, walletId?: string): Promise<boolean> {
    try {
      // Validate that amount is positive for spending
      if (amount <= 0) {
        throw new Error('Amount must be positive when spending coins');
      }

      // Use a transaction to ensure both operations succeed or fail together
      await db.transaction(async (tx) => {
        // Check current balance from the users table
        const [user] = await tx.select({ balance: users.dasWosCoinsBalance })
          .from(users)
          .where(eq(users.id, userId));

        if (!user || user.balance < amount) {
          throw new Error(`Insufficient balance. User has ${user?.balance || 0} coins, attempting to spend ${amount}`);
        }

        // Add spend transaction (negative amount)
        await tx.insert(dasWosCoinsTransactions).values({
          userId,
          amount: -amount, // Store as negative for spend transactions
          transactionType: 'spend',
          description,
          metadata: metadata ? JSON.stringify(metadata) : null,
          walletId: walletId || null, // Track which wallet was used
          createdAt: new Date()
        });

        // Update user's balance
        await tx
          .update(users)
          .set({
            dasWosCoinsBalance: sql`${users.dasWosCoinsBalance} - ${amount}`
          })
          .where(eq(users.id, userId));
      });

      return true;
    } catch (error) {
      console.error(`Error spending DasWos Coins for user ${userId}:`, error);
      return false;
    }
  }

  // DISABLED: Migration function for DasWos coins (moved to wallet system)
  async syncDasWosCoinsBalances(): Promise<void> {
    console.warn('syncDasWosCoinsBalances called but DasWos coins are now managed via wallet system');
    // No-op since DasWos coins are managed via external wallet system
  }

  async getDasWosCoinsTransactions(userId: number, limit?: number): Promise<DasWosCoinsTransaction[]> {
    try {
      // Build and execute query to get transactions for this user
      let transactions;

      if (limit && limit > 0) {
        transactions = await db.select()
          .from(dasWosCoinsTransactions)
          .where(eq(dasWosCoinsTransactions.userId, userId))
          .orderBy(desc(dasWosCoinsTransactions.createdAt))
          .limit(limit);
      } else {
        transactions = await db.select()
          .from(dasWosCoinsTransactions)
          .where(eq(dasWosCoinsTransactions.userId, userId))
          .orderBy(desc(dasWosCoinsTransactions.createdAt));
      }

      // Parse metadata from JSON string to object
      return transactions.map(tx => {
        try {
          // Safely handle metadata that could be string, object, or null
          let parsedMetadata;

          if (tx.metadata === null || tx.metadata === undefined) {
            parsedMetadata = null;
          } else if (typeof tx.metadata === 'object') {
            parsedMetadata = tx.metadata;
          } else if (typeof tx.metadata === 'string') {
            try {
              // Only try to parse if it looks like a JSON string (starts with { or [)
              if (tx.metadata.trim().startsWith('{') || tx.metadata.trim().startsWith('[')) {
                parsedMetadata = JSON.parse(tx.metadata);
              } else {
                // For non-JSON strings, keep as is
                parsedMetadata = tx.metadata;
              }
            } catch {
              // If parsing fails, keep original value
              parsedMetadata = tx.metadata;
            }
          } else {
            // For any other type, keep as is
            parsedMetadata = tx.metadata;
          }

          return {
            ...tx,
            metadata: parsedMetadata
          };
        } catch (error) {
          console.log(`Error parsing metadata for transaction ${tx.id}:`, error);
          // Return the transaction with original metadata to avoid breaking
          return {
            ...tx,
            metadata: null
          };
        }
      });
    } catch (error) {
      console.error(`Error getting DasWos Coins transactions for user ${userId}:`, error);
      return [];
    }
  }

  // Purchase Management
  async createPurchase(purchase: InsertPurchase): Promise<Purchase> {
    try {
      console.log(`🔄 DatabaseStorage.createPurchase called with:`, purchase);
      const [newPurchase] = await db.insert(purchases).values(purchase).returning();
      console.log(`✅ DatabaseStorage.createPurchase SUCCESS:`, newPurchase);
      return newPurchase;
    } catch (error) {
      console.error('❌ DatabaseStorage.createPurchase ERROR:', error);
      console.error('Purchase data that failed:', purchase);
      throw error;
    }
  }

  async getUserPurchases(userId: number): Promise<Purchase[]> {
    try {
      const userPurchases = await db.select()
        .from(purchases)
        .where(eq(purchases.buyerId, userId))
        .orderBy(desc(purchases.purchasedAt));
      return userPurchases;
    } catch (error) {
      console.error(`Error getting purchases for user ${userId}:`, error);
      return [];
    }
  }

  async markPurchaseAsReceived(purchaseId: number): Promise<void> {
    try {
      await db.update(purchases)
        .set({
          status: 'received',
          receivedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(purchases.id, purchaseId));
    } catch (error) {
      console.error(`Error marking purchase ${purchaseId} as received:`, error);
      throw error;
    }
  }

  // Submit rating for a purchase and update seller trust score
  async submitPurchaseRating(purchaseId: number, rating: number, comment?: string): Promise<Purchase> {
    console.log(`🔄 DatabaseStorage.submitPurchaseRating called for purchase ID ${purchaseId}, rating: ${rating}`);
    try {
      // Update the purchase with rating
      const [updatedPurchase] = await db
        .update(purchases)
        .set({
          rating: rating,
          reviewComment: comment || null,
          ratedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(purchases.id, purchaseId))
        .returning();

      if (!updatedPurchase) {
        throw new Error('Purchase not found');
      }

      console.log(`✅ DatabaseStorage.submitPurchaseRating SUCCESS: Purchase ${purchaseId} rated ${rating} stars`);

      // Update seller's trust score based on the rating
      await this.updateSellerTrustScore(updatedPurchase.sellerId, rating);

      return updatedPurchase;
    } catch (error) {
      console.error('❌ DatabaseStorage.submitPurchaseRating FAILED:', error);
      throw error;
    }
  }

  async getPurchaseById(purchaseId: number): Promise<Purchase | undefined> {
    try {
      const [purchase] = await db.select().from(purchases).where(eq(purchases.id, purchaseId));
      return purchase;
    } catch (error) {
      console.error(`Error getting purchase ${purchaseId}:`, error);
      return undefined;
    }
  }

  // Trust Score Calculation based on SELLER_TRUST_SCORE.md
  async updateSellerTrustScore(sellerId: number, rating: number): Promise<void> {
    console.log(`🔄 DatabaseStorage.updateSellerTrustScore called for seller ${sellerId}, rating: ${rating}`);
    try {
      // Get current seller data
      const [seller] = await db.select().from(users).where(eq(users.id, sellerId));
      if (!seller) {
        throw new Error('Seller not found');
      }

      console.log(`📊 Current seller data: ID=${seller.id}, trustScore=${seller.trustScore}`);

      // Get seller's purchase history to determine if this is their first positive sale
      const sellerPurchases = await db.select()
        .from(purchases)
        .where(and(
          eq(purchases.sellerId, sellerId),
          isNotNull(purchases.rating)
        ))
        .orderBy(purchases.ratedAt);

      console.log(`📈 Seller purchase history: ${sellerPurchases.length} rated purchases`);
      sellerPurchases.forEach((p, i) => {
        console.log(`  ${i + 1}. Purchase ID: ${p.id}, Rating: ${p.rating}, Date: ${p.ratedAt}`);
      });

      // Check if seller had a negative sale immediately before this one
      const previousPurchase = sellerPurchases.length > 1 ? sellerPurchases[sellerPurchases.length - 2] : null;
      const hadPreviousNegativeSale = previousPurchase && previousPurchase.rating !== null && previousPurchase.rating <= 2;

      console.log(`🔍 Previous purchase analysis: hadPreviousNegativeSale=${hadPreviousNegativeSale}, previousRating=${previousPurchase?.rating || 'none'}`);

      let pointsToAdd = 0;

      // Calculate points based on rating
      if (rating >= 4) {
        // Positive sale (4-5 stars)
        const positiveRatings = sellerPurchases.filter(p => p.rating !== null && p.rating >= 4);
        const isFirstPositiveSale = positiveRatings.length === 1; // This is the first one

        console.log(`🎯 Positive rating analysis: positiveRatings.length=${positiveRatings.length}, isFirstPositiveSale=${isFirstPositiveSale}`);

        // All positive sales now get +5 points (simplified system)
        pointsToAdd = 5;
        console.log(`🎉 Positive sale for seller ${sellerId}: +5 points (all positive sales get +5)`);

        if (isFirstPositiveSale) {
          console.log(`🌟 This is the seller's first positive sale!`);
        } else if (hadPreviousNegativeSale) {
          console.log(`🔄 This sale helps redeem previous negative feedback`);
        } else {
          console.log(`✅ Continuing positive sales streak`);
        }
      } else if (rating === 3) {
        // Neutral sale (3 stars): 0 points
        pointsToAdd = 0;
        console.log(`😐 Neutral sale for seller ${sellerId}: 0 points`);
      } else {
        // Negative sale (1-2 stars): -5 points
        pointsToAdd = -5;
        console.log(`❌ Negative sale for seller ${sellerId}: -5 points`);
      }

      console.log(`🧮 Trust score calculation: ${seller.trustScore} + ${pointsToAdd} = ${seller.trustScore + pointsToAdd}`);

      // Update seller's trust score
      const newTrustScore = Math.max(0, Math.min(100, seller.trustScore + pointsToAdd));

      console.log(`🎯 Final trust score (after bounds check): ${newTrustScore}`);

      await db
        .update(users)
        .set({
          trustScore: newTrustScore,
          updatedAt: new Date()
        })
        .where(eq(users.id, sellerId));

      console.log(`✅ DatabaseStorage.updateSellerTrustScore SUCCESS: Seller ${sellerId} trust score updated from ${seller.trustScore} to ${newTrustScore} (${pointsToAdd > 0 ? '+' : ''}${pointsToAdd} points)`);

    } catch (error) {
      console.error('❌ DatabaseStorage.updateSellerTrustScore FAILED:', error);
      throw error;
    }
  }

  async getPurchasesBySellerId(sellerId: number): Promise<Purchase[]> {
    try {
      const sellerPurchases = await db.select()
        .from(purchases)
        .where(eq(purchases.sellerId, sellerId))
        .orderBy(desc(purchases.purchasedAt));
      return sellerPurchases;
    } catch (error) {
      console.error(`Error getting purchases for seller ${sellerId}:`, error);
      return [];
    }
  }

  // Split Bulk Buy operations
  async createSplitBuy(splitBuy: InsertSplitBuy): Promise<SplitBuy> {
    try {
      // Add expiration date if not provided (default 7 days from now)
      const splitBuyWithDefaults = {
        ...splitBuy,
        expiresAt: splitBuy.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      };

      const [result] = await db.insert(splitBuys).values(splitBuyWithDefaults).returning();

      // Automatically add the initiator as the first participant
      await this.addSplitBuyParticipant({
        splitBuyId: result.id,
        userId: result.initiatorUserId,
        quantityCommitted: 1 // Default to 1, can be updated later
      });

      return result;
    } catch (error) {
      console.error("Error creating split buy:", error);
      throw error;
    }
  }

  async getSplitBuyById(id: number): Promise<SplitBuy | undefined> {
    try {
      const [splitBuy] = await db.select().from(splitBuys).where(eq(splitBuys.id, id));
      return splitBuy;
    } catch (error) {
      console.error("Error getting split buy by ID:", error);
      return undefined;
    }
  }

  async getSplitBuysByProductId(productId: number): Promise<SplitBuy[]> {
    try {
      return await db.select()
        .from(splitBuys)
        .where(and(
          eq(splitBuys.productId, productId),
          eq(splitBuys.status, "active")
        ))
        .orderBy(desc(splitBuys.createdAt));
    } catch (error) {
      console.error("Error getting split buys by product ID:", error);
      return [];
    }
  }

  async getSplitBuysByUserId(userId: number): Promise<SplitBuy[]> {
    try {
      // Get split buys where the user is either initiator or participant
      const initiatedSplitBuys = await db.select()
        .from(splitBuys)
        .where(eq(splitBuys.initiatorUserId, userId));

      // Get participant split buy IDs
      const participantEntries = await db.select()
        .from(splitBuyParticipants)
        .where(eq(splitBuyParticipants.userId, userId));

      if (participantEntries.length === 0) {
        return initiatedSplitBuys;
      }

      // Create array of split buy IDs from participant entries
      const participantIds = participantEntries.map(p => p.splitBuyId);

      // Only proceed if we have participant entries
      let participantSplitBuys = [];

      if (participantIds.length > 0) {
        // Get participant split buys
        // Using OR conditions for each id instead of IN operator
        participantSplitBuys = await db.select()
          .from(splitBuys)
          .where(
            and(
              or(...participantIds.map(id => eq(splitBuys.id, id))),
              not(eq(splitBuys.initiatorUserId, userId))
            )
          );
      }

      // Combine both lists, removing duplicates
      const allSplitBuys = [...initiatedSplitBuys];

      // Add participant split buys if not already in the list
      for (const splitBuy of participantSplitBuys) {
        if (!allSplitBuys.some(sb => sb.id === splitBuy.id)) {
          allSplitBuys.push(splitBuy);
        }
      }

      return allSplitBuys.sort((a, b) =>
        (b.createdAt?.getTime() || 0) - (a.createdAt?.getTime() || 0)
      );
    } catch (error) {
      console.error("Error getting split buys by user ID:", error);
      return [];
    }
  }

  async updateSplitBuyStatus(id: number, status: string): Promise<SplitBuy> {
    try {
      const [splitBuy] = await db
        .update(splitBuys)
        .set({
          status,
          updatedAt: new Date()
        })
        .where(eq(splitBuys.id, id))
        .returning();

      return splitBuy;
    } catch (error) {
      console.error("Error updating split buy status:", error);
      throw error;
    }
  }

  async addSplitBuyParticipant(participant: InsertSplitBuyParticipant): Promise<SplitBuyParticipant> {
    try {
      const [result] = await db.insert(splitBuyParticipants).values(participant).returning();

      // Update current quantity in the split buy
      const allParticipants = await this.getSplitBuyParticipants(participant.splitBuyId);
      const totalQuantity = allParticipants.reduce((sum, p) => sum + p.quantityCommitted, 0);

      // Update split buy with new quantity
      await db
        .update(splitBuys)
        .set({
          currentQuantity: totalQuantity,
          updatedAt: new Date()
        })
        .where(eq(splitBuys.id, participant.splitBuyId));

      return result;
    } catch (error) {
      console.error("Error adding split buy participant:", error);
      throw error;
    }
  }

  async getSplitBuyParticipants(splitBuyId: number): Promise<SplitBuyParticipant[]> {
    try {
      return await db.select()
        .from(splitBuyParticipants)
        .where(eq(splitBuyParticipants.splitBuyId, splitBuyId))
        .orderBy(asc(splitBuyParticipants.joinedAt));
    } catch (error) {
      console.error("Error getting split buy participants:", error);
      return [];
    }
  }

  async updateSplitBuyParticipantStatus(id: number, status: string): Promise<SplitBuyParticipant> {
    try {
      const [participant] = await db
        .update(splitBuyParticipants)
        .set({
          paymentStatus: status,
          updatedAt: new Date()
        })
        .where(eq(splitBuyParticipants.id, id))
        .returning();

      return participant;
    } catch (error) {
      console.error("Error updating split buy participant status:", error);
      throw error;
    }
  }

  // Order operations
  async createOrder(order: InsertOrder): Promise<Order> {
    try {
      const [result] = await db.insert(orders).values(order).returning();
      return result;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  }

  async getOrderById(id: number): Promise<Order | undefined> {
    try {
      const [order] = await db.select().from(orders).where(eq(orders.id, id));
      return order;
    } catch (error) {
      console.error("Error getting order by ID:", error);
      return undefined;
    }
  }

  async getOrdersByUserId(userId: number): Promise<Order[]> {
    try {
      return await db.select()
        .from(orders)
        .where(eq(orders.userId, userId))
        .orderBy(desc(orders.orderDate));
    } catch (error) {
      console.error("Error getting orders by user ID:", error);
      return [];
    }
  }

  async updateOrderStatus(id: number, status: string): Promise<Order> {
    try {
      const [order] = await db
        .update(orders)
        .set({
          status,
          updatedAt: new Date()
        })
        .where(eq(orders.id, id))
        .returning();

      return order;
    } catch (error) {
      console.error("Error updating order status:", error);
      throw error;
    }
  }

  async addOrderItem(orderItem: InsertOrderItem): Promise<OrderItem> {
    try {
      const [result] = await db.insert(orderItems).values(orderItem).returning();
      return result;
    } catch (error) {
      console.error("Error adding order item:", error);
      throw error;
    }
  }

  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    try {
      return await db.select()
        .from(orderItems)
        .where(eq(orderItems.orderId, orderId));
    } catch (error) {
      console.error("Error getting order items by order ID:", error);
      return [];
    }
  }

  // Daswos AI Chat operations
  async createDaswosAiChat(chat: InsertDaswosAiChat): Promise<DaswosAiChat> {
    try {
      const [newChat] = await db.insert(daswosAiChats).values(chat).returning();
      return newChat;
    } catch (error) {
      console.error("Error creating Daswos AI chat:", error);
      throw error;
    }
  }

  async getUserChats(userId: number | null): Promise<DaswosAiChat[]> {
    try {
      if (userId) {
        // Get authenticated user's chats
        return await db.select()
          .from(daswosAiChats)
          .where(and(
            eq(daswosAiChats.userId, userId),
            eq(daswosAiChats.isArchived, false)
          ))
          .orderBy(desc(daswosAiChats.updatedAt));
      } else {
        // Get guest chats (those without userId)
        return await db.select()
          .from(daswosAiChats)
          .where(and(
            isNotNull(daswosAiChats.userId),
            eq(daswosAiChats.isArchived, false)
          ))
          .orderBy(desc(daswosAiChats.updatedAt));
      }
    } catch (error) {
      console.error("Error getting user chats:", error);
      return [];
    }
  }

  async getChatById(chatId: number): Promise<DaswosAiChat | undefined> {
    try {
      const [chat] = await db.select()
        .from(daswosAiChats)
        .where(eq(daswosAiChats.id, chatId));
      return chat;
    } catch (error) {
      console.error("Error getting chat by ID:", error);
      return undefined;
    }
  }

  async updateChatTitle(chatId: number, title: string): Promise<DaswosAiChat> {
    try {
      const [updatedChat] = await db.update(daswosAiChats)
        .set({
          title,
          updatedAt: new Date()
        })
        .where(eq(daswosAiChats.id, chatId))
        .returning();
      return updatedChat;
    } catch (error) {
      console.error("Error updating chat title:", error);
      throw error;
    }
  }

  async archiveChat(chatId: number): Promise<DaswosAiChat> {
    try {
      const [archivedChat] = await db.update(daswosAiChats)
        .set({
          isArchived: true,
          updatedAt: new Date()
        })
        .where(eq(daswosAiChats.id, chatId))
        .returning();
      return archivedChat;
    } catch (error) {
      console.error("Error archiving chat:", error);
      throw error;
    }
  }

  // Daswos AI Chat Messages operations
  async addChatMessage(message: InsertDaswosAiChatMessage): Promise<DaswosAiChatMessage> {
    try {
      const [newMessage] = await db.insert(daswosAiChatMessages).values(message).returning();

      // Update the chat's updatedAt timestamp
      await db.update(daswosAiChats)
        .set({ updatedAt: new Date() })
        .where(eq(daswosAiChats.id, message.chatId));

      return newMessage;
    } catch (error) {
      console.error("Error adding chat message:", error);
      throw error;
    }
  }

  async getChatMessages(chatId: number): Promise<DaswosAiChatMessage[]> {
    try {
      return await db.select()
        .from(daswosAiChatMessages)
        .where(eq(daswosAiChatMessages.chatId, chatId))
        .orderBy(asc(daswosAiChatMessages.timestamp));
    } catch (error) {
      console.error("Error getting chat messages:", error);
      return [];
    }
  }

  async getRecentChatMessage(chatId: number): Promise<DaswosAiChatMessage | undefined> {
    try {
      const [message] = await db.select()
        .from(daswosAiChatMessages)
        .where(eq(daswosAiChatMessages.chatId, chatId))
        .orderBy(desc(daswosAiChatMessages.timestamp))
        .limit(1);
      return message;
    } catch (error) {
      console.error("Error getting recent chat message:", error);
      return undefined;
    }
  }

  // Daswos AI Sources operations
  async addMessageSource(source: InsertDaswosAiSource): Promise<DaswosAiSource> {
    try {
      const [newSource] = await db.insert(daswosAiSources).values(source).returning();
      return newSource;
    } catch (error) {
      console.error("Error adding message source:", error);
      throw error;
    }
  }

  async getMessageSources(messageId: number): Promise<DaswosAiSource[]> {
    try {
      return await db.select()
        .from(daswosAiSources)
        .where(eq(daswosAiSources.messageId, messageId))
        .orderBy(desc(daswosAiSources.relevanceScore));
    } catch (error) {
      console.error("Error getting message sources:", error);
      return [];
    }
  }

  // AutoShop operations
  async getAutoShopStatus(userId: string | number): Promise<any> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Check if we have a status in the app_settings table
      const settingKey = `autoshop_status_${userIdStr}`;
      const status = await this.getAppSettings(settingKey);

      return status || { active: false };
    } catch (error) {
      console.error(`Error getting AutoShop status for user ${userId}:`, error);
      return { active: false };
    }
  }

  async setAutoShopStatus(userId: string | number, status: any): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Store status in the app_settings table
      const settingKey = `autoshop_status_${userIdStr}`;
      return await this.setAppSettings(settingKey, status);
    } catch (error) {
      console.error(`Error setting AutoShop status for user ${userId}:`, error);
      return false;
    }
  }

  async getAutoShopSettings(userId: string | number): Promise<any> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Check if we have settings in the app_settings table
      const settingKey = `autoshop_settings_${userIdStr}`;
      return await this.getAppSettings(settingKey);
    } catch (error) {
      console.error(`Error getting AutoShop settings for user ${userId}:`, error);
      return null;
    }
  }

  async setAutoShopSettings(userId: string | number, settings: any): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Store settings in the app_settings table
      const settingKey = `autoshop_settings_${userIdStr}`;
      return await this.setAppSettings(settingKey, settings);
    } catch (error) {
      console.error(`Error setting AutoShop settings for user ${userId}:`, error);
      return false;
    }
  }

  async getAutoShopPendingItems(userId: string | number): Promise<any[]> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Check if we have pending items in the app_settings table
      const settingKey = `autoshop_pending_items_${userIdStr}`;
      const items = await this.getAppSettings(settingKey);

      return items || [];
    } catch (error) {
      console.error(`Error getting AutoShop pending items for user ${userId}:`, error);
      return [];
    }
  }

  async addAutoShopItem(userId: string | number, item: any): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Get current pending items
      const settingKey = `autoshop_pending_items_${userIdStr}`;
      const currentItems = await this.getAppSettings(settingKey) || [];

      // Add the new item
      currentItems.push(item);

      // Store updated items
      return await this.setAppSettings(settingKey, currentItems);
    } catch (error) {
      console.error(`Error adding AutoShop item for user ${userId}:`, error);
      return false;
    }
  }

  async removeAutoShopItem(userId: string | number, itemId: string): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Get current pending items
      const settingKey = `autoshop_pending_items_${userIdStr}`;
      const currentItems = await this.getAppSettings(settingKey) || [];

      // Remove the item
      const updatedItems = currentItems.filter((item: any) => item.id !== itemId);

      // Store updated items
      return await this.setAppSettings(settingKey, updatedItems);
    } catch (error) {
      console.error(`Error removing AutoShop item for user ${userId}:`, error);
      return false;
    }
  }

  async clearAutoShopItems(userId: string | number): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Clear pending items
      const settingKey = `autoshop_pending_items_${userIdStr}`;
      return await this.setAppSettings(settingKey, []);
    } catch (error) {
      console.error(`Error clearing AutoShop items for user ${userId}:`, error);
      return false;
    }
  }

  async getAutoShopOrderHistory(userId: string | number): Promise<any[]> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Check if we have order history in the app_settings table
      const settingKey = `autoshop_order_history_${userIdStr}`;
      const history = await this.getAppSettings(settingKey);

      return history || [];
    } catch (error) {
      console.error(`Error getting AutoShop order history for user ${userId}:`, error);
      return [];
    }
  }

  async addAutoShopOrderHistoryItem(userId: string | number, item: any): Promise<boolean> {
    try {
      // Convert userId to string for consistent handling
      const userIdStr = userId.toString();

      // Get current order history
      const settingKey = `autoshop_order_history_${userIdStr}`;
      const currentHistory = await this.getAppSettings(settingKey) || [];

      // Add the new item
      currentHistory.push(item);

      // Store updated history
      return await this.setAppSettings(settingKey, currentHistory);
    } catch (error) {
      console.error(`Error adding AutoShop order history item for user ${userId}:`, error);
      return false;
    }
  }

  // Family invitation code operations
  async createFamilyInvitationCode(ownerId: number, email?: string, expiresInDays: number = 7): Promise<FamilyInvitationCode> {
    try {
      // Generate a random 8-character code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();

      // Set expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);

      // Create the invitation code
      const [invitation] = await db.insert(familyInvitationCodes).values({
        code,
        ownerUserId: ownerId,
        email,
        expiresAt,
        isUsed: false
      }).returning();

      return invitation;
    } catch (error) {
      console.error("Error creating family invitation code:", error);
      throw error;
    }
  }

  async getFamilyInvitationByCode(code: string): Promise<FamilyInvitationCode | undefined> {
    try {
      const [invitation] = await db
        .select()
        .from(familyInvitationCodes)
        .where(eq(familyInvitationCodes.code, code));

      return invitation;
    } catch (error) {
      console.error("Error getting family invitation by code:", error);
      throw error;
    }
  }

  async getFamilyInvitationsByOwner(ownerId: number): Promise<FamilyInvitationCode[]> {
    try {
      return await db
        .select()
        .from(familyInvitationCodes)
        .where(eq(familyInvitationCodes.ownerUserId, ownerId));
    } catch (error) {
      console.error("Error getting family invitations by owner:", error);
      throw error;
    }
  }

  async markFamilyInvitationAsUsed(code: string, userId: number): Promise<boolean> {
    try {
      const [invitation] = await db
        .update(familyInvitationCodes)
        .set({
          isUsed: true,
          usedByUserId: userId
        })
        .where(eq(familyInvitationCodes.code, code))
        .returning();

      return !!invitation;
    } catch (error) {
      console.error("Error marking family invitation as used:", error);
      throw error;
    }
  }

  async updateFamilyMemberSettings(ownerId: number, memberId: number, settings: any): Promise<boolean> {
    try {
      // First verify that the ownerId is the family owner of memberId
      const member = await this.getUser(memberId);
      if (!member || member.familyOwnerId !== ownerId) {
        return false;
      }

      // Update the member's settings
      await db
        .update(users)
        .set(settings)
        .where(eq(users.id, memberId));

      return true;
    } catch (error) {
      console.error("Error updating family member settings:", error);
      return false;
    }
  }

  // User Settings operations
  async getDasbarSettings(userId: number): Promise<any> {
    try {
      // First check if we have settings stored in app_settings
      const key = `user_${userId}_dasbar_settings`;
      const [settings] = await db.select()
        .from(appSettings)
        .where(eq(appSettings.key, key));

      if (settings) {
        return settings.value;
      }

      // Return default settings if none found
      return {
        enabled: true,
        autoRefresh: false,
        refreshInterval: 30,
        notifications: true
      };
    } catch (error) {
      console.error("Error getting DasBar settings:", error);
      // Return default settings on error
      return {
        enabled: true,
        autoRefresh: false,
        refreshInterval: 30,
        notifications: true
      };
    }
  }

  async updateDasbarSettings(settings: any): Promise<any> {
    try {
      const { userId, ...settingsData } = settings;
      const key = `user_${userId}_dasbar_settings`;

      // Check if settings already exist
      const [existingSettings] = await db.select()
        .from(appSettings)
        .where(eq(appSettings.key, key));

      if (existingSettings) {
        // Update existing settings
        await db.update(appSettings)
          .set({
            value: settingsData,
            updatedAt: new Date()
          })
          .where(eq(appSettings.key, key));
      } else {
        // Create new settings
        await db.insert(appSettings)
          .values({
            key,
            value: settingsData,
            createdAt: new Date(),
            updatedAt: new Date()
          });
      }

      return settingsData;
    } catch (error) {
      console.error("Error updating DasBar settings:", error);
      throw error;
    }
  }



  // User session methods
  async createUserSession(sessionData: {
    userId: number;
    sessionToken: string;
    deviceInfo: any;
    expiresAt: Date;
  }) {
    try {
      // Check if the sessions table exists
      try {
        await db.execute(sql`
          CREATE TABLE IF NOT EXISTS user_sessions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            session_token TEXT NOT NULL,
            device_info JSONB,
            is_active BOOLEAN DEFAULT TRUE,
            expires_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )
        `);
      } catch (tableError) {
        // Continue anyway, as the table might already exist
      }

      // Insert the session into the database
      try {
        const [session] = await db.execute(sql`
          INSERT INTO user_sessions (
            user_id,
            session_token,
            device_info,
            is_active,
            expires_at
          )
          VALUES (
            ${sessionData.userId},
            ${sessionData.sessionToken},
            ${JSON.stringify(sessionData.deviceInfo)},
            TRUE,
            ${sessionData.expiresAt}
          )
          RETURNING *
        `);

        return session || { id: 1, ...sessionData };
      } catch (insertError) {
        // Return a mock session if database insert fails
        return { id: 1, ...sessionData };
      }
    } catch (error) {
      // Return a mock session as fallback
      return { id: 1, ...sessionData };
    }
  }

  async getUserSessions(userId: number | null) {
    try {
      // If userId is null, return all sessions (for token validation)
      if (userId === null) {
        const sessions = await db.execute(sql`
          SELECT * FROM user_sessions
          WHERE is_active = TRUE AND expires_at > NOW()
        `);
        return sessions || [];
      }

      // Get sessions for a specific user
      const sessions = await db.execute(sql`
        SELECT * FROM user_sessions
        WHERE user_id = ${userId} AND is_active = TRUE
        ORDER BY created_at DESC
      `);

      return sessions || [];
    } catch (error) {
      return [];
    }
  }

  async deactivateSession(sessionToken: string) {
    try {
      await db.execute(sql`
        UPDATE user_sessions
        SET is_active = FALSE
        WHERE session_token = ${sessionToken}
      `);

      return true;
    } catch (error) {
      return false;
    }
  }

  async deactivateAllUserSessions(userId: number) {
    try {
      await db.execute(sql`
        UPDATE user_sessions
        SET is_active = FALSE
        WHERE user_id = ${userId}
      `);

      return true;
    } catch (error) {
      return false;
    }
  }
}

const databaseStorage = new DatabaseStorage();
const memoryStorageInstance = memoryStorage;

// Streamlined HybridStorage class that implements our simplified IStorage interface
export class HybridStorage implements IStorage {
  private primaryStorage: DatabaseStorage;
  private fallbackStorage: FallbackStorage;

  constructor() {
    this.primaryStorage = new DatabaseStorage();
    this.fallbackStorage = memoryStorage;
  }

  // Proxy the sessionStore property
  get sessionStore() {
    return this.primaryStorage.sessionStore;
  }

  // Helper method to execute a storage operation with fallback
  private async executeWithFallback<T>(
    operation: string,
    primaryFn: () => Promise<T>,
    fallbackFn: () => Promise<T>
  ): Promise<T> {
    try {
      const result = await primaryFn();
      return result;
    } catch (error) {
      console.log(`⚠️ ${operation} failed, using fallback storage`);
      const fallbackResult = await fallbackFn();
      return fallbackResult;
    }
  }

  // Test database connectivity for transaction safety
  async testConnection(): Promise<void> {
    try {
      await this.primaryStorage.testConnection();
    } catch (error) {
      // If primary storage fails, test fallback (which should always succeed)
      // But still throw the error since we need primary storage for transactions
      throw error;
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.executeWithFallback(
      'getUser',
      () => this.primaryStorage.getUser(id),
      () => this.fallbackStorage.getUser(id)
    );
  }

  async getUserById(id: number): Promise<User | undefined> {
    return this.executeWithFallback(
      'getUserById',
      () => this.primaryStorage.getUserById(id),
      () => this.fallbackStorage.getUserById(id)
    );
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.executeWithFallback(
      'getUserByUsername',
      () => this.primaryStorage.getUserByUsername(username),
      () => this.fallbackStorage.getUserByUsername(username)
    );
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return this.executeWithFallback(
      'getUserByEmail',
      () => this.primaryStorage.getUserByEmail(email),
      () => this.fallbackStorage.getUserByEmail(email)
    );
  }

  async createUser(user: InsertUser): Promise<User> {
    return this.executeWithFallback(
      'createUser',
      () => this.primaryStorage.createUser(user),
      () => this.fallbackStorage.createUser(user)
    );
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    return this.executeWithFallback(
      'updateUser',
      () => this.primaryStorage.updateUser(id, updates),
      () => this.fallbackStorage.updateUser(id, updates)
    );
  }

  async updateUserSellerStatus(userId: number, isSeller: boolean): Promise<boolean> {
    return this.executeWithFallback(
      'updateUserSellerStatus',
      () => this.primaryStorage.updateUserSellerStatus(userId, isSeller),
      () => this.fallbackStorage.updateUserSellerStatus(userId, isSeller)
    );
  }

  // Product operations
  async getProducts(sphere: string, query?: string, categories?: string[]): Promise<Product[]> {
    return this.executeWithFallback(
      'getProducts',
      () => this.primaryStorage.getProducts(sphere, query, categories),
      () => this.fallbackStorage.getProducts(sphere, query, categories)
    );
  }

  async getProductById(id: number): Promise<Product | undefined> {
    return this.executeWithFallback(
      'getProductById',
      () => this.primaryStorage.getProductById(id),
      () => this.fallbackStorage.getProductById(id)
    );
  }

  async getProductsByCategory(categoryName: string): Promise<Product[]> {
    return this.executeWithFallback(
      'getProductsByCategory',
      () => this.primaryStorage.getProductsByCategory(categoryName),
      () => this.fallbackStorage.getProducts('all', '', [categoryName])
    );
  }

  async createProduct(product: any): Promise<Product> {
    return this.executeWithFallback(
      'createProduct',
      () => this.primaryStorage.createProduct(product),
      () => this.fallbackStorage.createProduct(product)
    );
  }

  async getProductsBySellerId(sellerId: number): Promise<Product[]> {
    return this.executeWithFallback(
      'getProductsBySellerId',
      () => this.primaryStorage.getProductsBySellerId(sellerId),
      () => this.fallbackStorage.getProductsBySellerId(sellerId)
    );
  }

  async updateProductStatus(productId: number, status: string, soldQuantity?: number): Promise<Product | undefined> {
    return this.executeWithFallback(
      'updateProductStatus',
      () => this.primaryStorage.updateProductStatus(productId, status, soldQuantity),
      () => this.fallbackStorage.updateProductStatus(productId, status, soldQuantity)
    );
  }

  async reserveProductInventory(productId: number, requestedQuantity: number, userId: number): Promise<{
    success: boolean;
    error?: string;
    product?: Product;
    available?: number;
  }> {
    try {
      // Only use database storage for inventory operations - too critical for fallback
      return await (this.primaryStorage as DatabaseStorage).reserveProductInventory(productId, requestedQuantity, userId);
    } catch (error) {
      console.error('Critical: Database unavailable for inventory reservation:', error);
      return {
        success: false,
        error: 'Database unavailable - cannot reserve inventory safely',
        available: 0
      };
    }
  }

  async releaseProductInventory(productId: number, quantityToRelease: number): Promise<boolean> {
    try {
      // Only use database storage for inventory operations - too critical for fallback
      return await (this.primaryStorage as DatabaseStorage).releaseProductInventory(productId, quantityToRelease);
    } catch (error) {
      console.error('Critical: Database unavailable for inventory release:', error);
      return false;
    }
  }

  // AI operations
  async generateAiRecommendations(
    userId: number | null,
    preferredCategories?: string[],
    minPrice?: number,
    maxPrice?: number,
    useRandomMode?: boolean
  ): Promise<any[]> {
    return this.executeWithFallback(
      'generateAiRecommendations',
      () => this.primaryStorage.generateAiRecommendations(userId, preferredCategories, minPrice, maxPrice, useRandomMode),
      () => this.fallbackStorage.generateAiRecommendations(userId, preferredCategories, minPrice, maxPrice, useRandomMode)
    );
  }

  async getAiRecommendations(userId: number | null): Promise<any[]> {
    return this.executeWithFallback(
      'getAiRecommendations',
      () => this.primaryStorage.getAiRecommendations(userId),
      () => this.fallbackStorage.getAiRecommendations(userId)
    );
  }

  // User history operations
  async addUserPurchaseHistory(
    userId: number,
    productId: number,
    categoryId: number | null,
    price: number,
    quantity?: number
  ): Promise<any> {
    return this.executeWithFallback(
      'addUserPurchaseHistory',
      () => this.primaryStorage.addUserPurchaseHistory(userId, productId, categoryId, price, quantity),
      () => this.fallbackStorage.addUserPurchaseHistory(userId, productId, categoryId, price, quantity)
    );
  }

  async getUserPurchaseHistory(userId: number, limit?: number): Promise<any[]> {
    return this.executeWithFallback(
      'getUserPurchaseHistory',
      () => this.primaryStorage.getUserPurchaseHistory(userId, limit),
      () => this.fallbackStorage.getUserPurchaseHistory(userId, limit)
    );
  }

  async addUserSearchHistory(
    userId: number,
    searchQuery: string,
    categoryId?: number | null,
    clickedProductId?: number | null
  ): Promise<any> {
    return this.executeWithFallback(
      'addUserSearchHistory',
      () => this.primaryStorage.addUserSearchHistory(userId, searchQuery, categoryId, clickedProductId),
      () => this.fallbackStorage.addUserSearchHistory(userId, searchQuery, categoryId, clickedProductId)
    );
  }

  async getUserSearchHistory(userId: number, limit?: number): Promise<any[]> {
    return this.executeWithFallback(
      'getUserSearchHistory',
      () => this.primaryStorage.getUserSearchHistory(userId, limit),
      () => this.fallbackStorage.getUserSearchHistory(userId, limit)
    );
  }

  async getUserProductPreferences(userId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getUserProductPreferences',
      () => this.primaryStorage.getUserProductPreferences(userId),
      () => this.fallbackStorage.getUserProductPreferences(userId)
    );
  }

  async updateUserProductPreference(userId: number, categoryId: number, preferenceScore: number): Promise<any> {
    return this.executeWithFallback(
      'updateUserProductPreference',
      () => this.primaryStorage.updateUserProductPreference(userId, categoryId, preferenceScore),
      () => this.fallbackStorage.updateUserProductPreference(userId, categoryId, preferenceScore)
    );
  }

  // Information content operations
  async getInformationContent(query?: string, category?: string): Promise<InformationContent[]> {
    return this.executeWithFallback(
      'getInformationContent',
      () => this.primaryStorage.getInformationContent(query, category),
      () => this.fallbackStorage.getInformationContent(query, category)
    );
  }

  async getInformationContentById(id: number): Promise<InformationContent | undefined> {
    return this.executeWithFallback(
      'getInformationContentById',
      () => this.primaryStorage.getInformationContentById(id),
      () => this.fallbackStorage.getInformationContentById(id)
    );
  }

  // User session operations
  async createUserSession(session: any): Promise<any> {
    return this.executeWithFallback(
      'createUserSession',
      () => this.primaryStorage.createUserSession(session),
      () => this.fallbackStorage.createUserSession(session)
    );
  }

  async getUserSessions(userId: number | null): Promise<any[]> {
    return this.executeWithFallback(
      'getUserSessions',
      () => this.primaryStorage.getUserSessions(userId),
      () => this.fallbackStorage.getUserSessions(userId)
    );
  }

  async deactivateSession(sessionToken: string): Promise<boolean> {
    return this.executeWithFallback(
      'deactivateSession',
      () => this.primaryStorage.deactivateSession(sessionToken),
      () => this.fallbackStorage.deactivateSession(sessionToken)
    );
  }

  async deactivateAllUserSessions(userId: number): Promise<boolean> {
    return this.executeWithFallback(
      'deactivateAllUserSessions',
      () => this.primaryStorage.deactivateAllUserSessions(userId),
      () => this.fallbackStorage.deactivateAllUserSessions(userId)
    );
  }

  // App settings operations
  async getAppSettings(key: string): Promise<any> {
    return this.executeWithFallback(
      'getAppSettings',
      () => this.primaryStorage.getAppSettings(key),
      () => this.fallbackStorage.getAppSettings(key)
    );
  }

  async setAppSettings(key: string, value: any): Promise<boolean> {
    return this.executeWithFallback(
      'setAppSettings',
      () => this.primaryStorage.setAppSettings(key, value),
      () => this.fallbackStorage.setAppSettings(key, value)
    );
  }

  async getAllAppSettings(): Promise<{[key: string]: any}> {
    return this.executeWithFallback(
      'getAllAppSettings',
      () => this.primaryStorage.getAllAppSettings(),
      () => this.fallbackStorage.getAllAppSettings()
    );
  }

  // User subscription operations
  async updateUserSubscription(userId: number, subscriptionType: string, durationMonths: number): Promise<User> {
    return this.executeWithFallback(
      'updateUserSubscription',
      () => this.primaryStorage.updateUserSubscription(userId, subscriptionType, durationMonths),
      () => this.fallbackStorage.updateUserSubscription(userId, subscriptionType, durationMonths)
    );
  }

  async checkUserHasSubscription(userId: number): Promise<boolean> {
    return this.executeWithFallback(
      'checkUserHasSubscription',
      () => this.primaryStorage.checkUserHasSubscription(userId),
      () => this.fallbackStorage.checkUserHasSubscription(userId)
    );
  }

  async getUserSubscriptionDetails(userId: number): Promise<{hasSubscription: boolean, type?: string, expiresAt?: Date}> {
    return this.executeWithFallback(
      'getUserSubscriptionDetails',
      () => this.primaryStorage.getUserSubscriptionDetails(userId),
      () => this.fallbackStorage.getUserSubscriptionDetails(userId)
    );
  }

  // DasWos Coins operations
  async getUserDasWosCoins(userId: number): Promise<number> {
    return this.executeWithFallback(
      'getUserDasWosCoins',
      () => this.primaryStorage.getUserDasWosCoins(userId),
      () => this.fallbackStorage.getUserDasWosCoins(userId)
    );
  }

  async addDasWosCoins(userId: number, amount: number, type: string, description: string, metadata?: any, walletId?: string): Promise<boolean> {
    return this.executeWithFallback(
      'addDasWosCoins',
      () => this.primaryStorage.addDasWosCoins(userId, amount, type, description, metadata, walletId),
      () => this.fallbackStorage.addDasWosCoins(userId, amount, type, description, metadata)
    );
  }

  async spendDasWosCoins(userId: number, amount: number, description: string, metadata?: any, walletId?: string): Promise<boolean> {
    return this.executeWithFallback(
      'spendDasWosCoins',
      () => this.primaryStorage.spendDasWosCoins(userId, amount, description, metadata, walletId),
      () => this.fallbackStorage.spendDasWosCoins(userId, amount, description, metadata)
    );
  }

  async getDasWosCoinsTransactions(userId: number, limit?: number): Promise<any[]> {
    return this.executeWithFallback(
      'getDasWosCoinsTransactions',
      () => this.primaryStorage.getDasWosCoinsTransactions(userId, limit),
      () => this.fallbackStorage.getDasWosCoinsTransactions(userId, limit)
    );
  }

  // SECURITY: Check if user's wallet matches (for balance access control)
  async checkUserWalletAccess(userId: number, walletId?: string): Promise<{ hasAccess: boolean; userWalletId?: string; requiresWalletLink?: boolean }> {
    return this.executeWithFallback(
      'checkUserWalletAccess',
      () => this.primaryStorage.checkUserWalletAccess(userId, walletId),
      () => {
        // Fallback storage doesn't have wallet linking - always deny access for security
        console.log(`🚫 FALLBACK SECURITY: Wallet access denied for user ${userId} - fallback storage doesn't support wallet linking`);
        return { hasAccess: false, requiresWalletLink: true };
      }
    );
  }

  // Purchase Management
  async createPurchase(purchase: any): Promise<any> {
    return this.executeWithFallback(
      'createPurchase',
      () => this.primaryStorage.createPurchase(purchase),
      () => this.fallbackStorage.createPurchase(purchase)
    );
  }

  async getUserPurchases(userId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getUserPurchases',
      () => this.primaryStorage.getUserPurchases(userId),
      () => this.fallbackStorage.getUserPurchases(userId)
    );
  }

  async markPurchaseAsReceived(purchaseId: number): Promise<void> {
    return this.executeWithFallback(
      'markPurchaseAsReceived',
      () => this.primaryStorage.markPurchaseAsReceived(purchaseId),
      () => this.fallbackStorage.markPurchaseAsReceived(purchaseId)
    );
  }

  async getPurchaseById(purchaseId: number): Promise<any | undefined> {
    return this.executeWithFallback(
      'getPurchaseById',
      () => this.primaryStorage.getPurchaseById(purchaseId),
      () => this.fallbackStorage.getPurchaseById(purchaseId)
    );
  }

  async submitPurchaseRating(purchaseId: number, rating: number, comment?: string): Promise<any> {
    console.log(`🔄 HybridStorage.submitPurchaseRating called for purchase ${purchaseId}, rating: ${rating}`);
    return this.executeWithFallback(
      'submitPurchaseRating',
      async () => {
        console.log(`🎯 Using PRIMARY storage (DatabaseStorage) for submitPurchaseRating`);
        return await this.primaryStorage.submitPurchaseRating(purchaseId, rating, comment);
      },
      async () => {
        console.log(`⚠️ Using FALLBACK storage (FallbackStorage) for submitPurchaseRating`);
        return await this.fallbackStorage.submitPurchaseRating(purchaseId, rating, comment);
      }
    );
  }

  async getPurchasesBySellerId(sellerId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getPurchasesBySellerId',
      () => this.primaryStorage.getPurchasesBySellerId(sellerId),
      () => this.fallbackStorage.getPurchasesBySellerId(sellerId)
    );
  }

  // Cart operations
  async getUserCartItems(userId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getUserCartItems',
      () => this.primaryStorage.getUserCartItems(userId),
      () => this.fallbackStorage.getUserCartItems(userId)
    );
  }

  async addCartItem(cartItem: any): Promise<any> {
    return this.executeWithFallback(
      'addCartItem',
      () => this.primaryStorage.addCartItem(cartItem),
      () => this.fallbackStorage.addCartItem(cartItem)
    );
  }

  async updateCartItemQuantity(itemId: number, quantity: number): Promise<any> {
    return this.executeWithFallback(
      'updateCartItemQuantity',
      () => this.primaryStorage.updateCartItemQuantity(itemId, quantity),
      () => this.fallbackStorage.updateCartItemQuantity(itemId, quantity)
    );
  }

  async removeCartItem(itemId: number): Promise<boolean> {
    return this.executeWithFallback(
      'removeCartItem',
      async () => {
        await this.primaryStorage.removeCartItem(itemId);
        return true;
      },
      () => this.fallbackStorage.removeCartItem(itemId)
    );
  }

  async clearUserCart(userId: number): Promise<boolean> {
    return this.executeWithFallback(
      'clearUserCart',
      async () => {
        await this.primaryStorage.clearUserCart(userId);
        return true;
      },
      () => this.fallbackStorage.clearUserCart(userId)
    );
  }

  // SafeSphere operations
  async getSafeSphereStatus(userId: number): Promise<{ active: boolean }> {
    return this.executeWithFallback(
      'getSafeSphereStatus',
      async () => {
        const active = await this.primaryStorage.getSafeSphereStatus(userId);
        return { active };
      },
      () => this.fallbackStorage.getSafeSphereStatus(userId)
    );
  }

  async updateSafeSphereStatus(userId: number, active: boolean): Promise<boolean> {
    return this.executeWithFallback(
      'updateSafeSphereStatus',
      () => this.primaryStorage.updateSafeSphereStatus(userId, active),
      () => this.fallbackStorage.updateSafeSphereStatus(userId, active)
    );
  }

  // Dasbar preferences operations
  async getUserDasbarPreferences(userId: number): Promise<any> {
    return this.executeWithFallback(
      'getUserDasbarPreferences',
      () => this.primaryStorage.getUserDasbarPreferences(userId),
      () => this.fallbackStorage.getUserDasbarPreferences(userId)
    );
  }

  async saveUserDasbarPreferences(userId: number, items: any[]): Promise<any> {
    return this.executeWithFallback(
      'saveUserDasbarPreferences',
      () => this.primaryStorage.saveUserDasbarPreferences(userId, items),
      () => this.fallbackStorage.saveUserDasbarPreferences(userId, items)
    );
  }



  // AutoShop operations
  async getAutoShopStatus(userId: string | number): Promise<any> {
    return this.executeWithFallback(
      'getAutoShopStatus',
      () => this.primaryStorage.getAutoShopStatus(userId),
      () => this.fallbackStorage.getAutoShopStatus(userId)
    );
  }

  async setAutoShopStatus(userId: string | number, status: any): Promise<boolean> {
    return this.executeWithFallback(
      'setAutoShopStatus',
      () => this.primaryStorage.setAutoShopStatus(userId, status),
      () => this.fallbackStorage.setAutoShopStatus(userId, status)
    );
  }

  async getAutoShopSettings(userId: string | number): Promise<any> {
    return this.executeWithFallback(
      'getAutoShopSettings',
      () => this.primaryStorage.getAutoShopSettings(userId),
      () => this.fallbackStorage.getAutoShopSettings(userId)
    );
  }

  async setAutoShopSettings(userId: string | number, settings: any): Promise<boolean> {
    return this.executeWithFallback(
      'setAutoShopSettings',
      () => this.primaryStorage.setAutoShopSettings(userId, settings),
      () => this.fallbackStorage.setAutoShopSettings(userId, settings)
    );
  }

  async getAutoShopPendingItems(userId: string | number): Promise<any[]> {
    return this.executeWithFallback(
      'getAutoShopPendingItems',
      () => this.primaryStorage.getAutoShopPendingItems(userId),
      () => this.fallbackStorage.getAutoShopPendingItems(userId)
    );
  }

  async addAutoShopItem(userId: string | number, item: any): Promise<boolean> {
    return this.executeWithFallback(
      'addAutoShopItem',
      () => this.primaryStorage.addAutoShopItem(userId, item),
      () => this.fallbackStorage.addAutoShopItem(userId, item)
    );
  }

  async removeAutoShopItem(userId: string | number, itemId: string): Promise<boolean> {
    return this.executeWithFallback(
      'removeAutoShopItem',
      () => this.primaryStorage.removeAutoShopItem(userId, itemId),
      () => this.fallbackStorage.removeAutoShopItem(userId, itemId)
    );
  }

  async clearAutoShopItems(userId: string | number): Promise<boolean> {
    return this.executeWithFallback(
      'clearAutoShopItems',
      () => this.primaryStorage.clearAutoShopItems(userId),
      () => this.fallbackStorage.clearAutoShopItems(userId)
    );
  }

  async getAutoShopOrderHistory(userId: string | number): Promise<any[]> {
    return this.executeWithFallback(
      'getAutoShopOrderHistory',
      () => this.primaryStorage.getAutoShopOrderHistory(userId),
      () => this.fallbackStorage.getAutoShopOrderHistory(userId)
    );
  }

  async addAutoShopOrderHistoryItem(userId: string | number, item: any): Promise<boolean> {
    return this.executeWithFallback(
      'addAutoShopOrderHistoryItem',
      () => this.primaryStorage.addAutoShopOrderHistoryItem(userId, item),
      () => this.fallbackStorage.addAutoShopOrderHistoryItem(userId, item)
    );
  }

  // Order operations
  async createOrder(order: any): Promise<any> {
    return this.executeWithFallback(
      'createOrder',
      () => this.primaryStorage.createOrder(order),
      () => this.fallbackStorage.createOrder(order)
    );
  }

  async getOrderById(id: number): Promise<any> {
    return this.executeWithFallback(
      'getOrderById',
      () => this.primaryStorage.getOrderById(id),
      () => this.fallbackStorage.getOrderById(id)
    );
  }

  async getOrdersByUserId(userId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getOrdersByUserId',
      () => this.primaryStorage.getOrdersByUserId(userId),
      () => this.fallbackStorage.getOrdersByUserId(userId)
    );
  }

  async updateOrderStatus(id: number, status: string): Promise<any> {
    return this.executeWithFallback(
      'updateOrderStatus',
      () => this.primaryStorage.updateOrderStatus(id, status),
      () => this.fallbackStorage.updateOrderStatus(id, status)
    );
  }

  async addOrderItem(orderItem: any): Promise<any> {
    return this.executeWithFallback(
      'addOrderItem',
      () => this.primaryStorage.addOrderItem(orderItem),
      () => this.fallbackStorage.addOrderItem(orderItem)
    );
  }

  async getOrderItemsByOrderId(orderId: number): Promise<any[]> {
    return this.executeWithFallback(
      'getOrderItemsByOrderId',
      () => this.primaryStorage.getOrderItemsByOrderId(orderId),
      () => this.fallbackStorage.getOrderItemsByOrderId(orderId)
    );
  }
}

// Create a storage instance
export const storage = new HybridStorage();

// Log storage configuration
if (process.env.NODE_ENV === 'production') {
  log('Production mode active: Using PostgreSQL exclusively with no fallbacks', 'info');
} else {
  log('Development mode active: Primary storage is PostgreSQL with memory fallback', 'info');
}

