/* Import custom styles */
@import url('./styles/select-fix.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hologram Effects */
@keyframes hologramFlicker {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.9; }
  75% { opacity: 0.7; }
}

@keyframes scanLine {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(500%); }
}

@keyframes hologramBeam {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scaleY(1);
  }
  50% {
    opacity: 0.9;
    transform: translateX(-50%) scaleY(1.1);
  }
}

@keyframes hologramFloat {
  0%, 100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0);
  }
  50% {
    transform: translateX(-50%) translateY(-52%) translateZ(0);
  }
}

/* Global padding for dasbar */
.page-content {
  padding-bottom: 80px !important; /* Ensure content isn't hidden behind the dasbar */
  padding-left: 80px !important; /* Add padding to the left to avoid overlap with the dasbar buttons */
}

/* Main content padding for all pages */
main {
  padding-left: 80px !important; /* Add padding to the left to avoid overlap with the dasbar buttons */
}

@media (max-width: 768px) {
  main {
    padding-left: 70px !important; /* Slightly less padding on smaller screens */
  }
}

@media (max-width: 480px) {
  main {
    padding-left: 60px !important; /* Even less padding on very small screens */
  }
}

/* AI Assistant page specific styles */
.page-ai-assistant {
  padding-bottom: 20px !important; /* Reduced padding for AI Assistant page */
  min-height: calc(100vh - 140px); /* Fit to viewport height minus header and some spacing */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
}

/* Custom styles for das.list page */
.daslist-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px); /* Adjust for header and navigation */
  overflow: hidden;
  padding: 0 20px;
}

.daslist-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  padding: 0;
  margin-bottom: 80px; /* Space for the navigation bar */
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Category key styles */
.category-key {
  background-color: rgba(240, 240, 240, 0.5);
  border-radius: 8px;
  padding: 8px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .category-key {
  background-color: rgba(34, 34, 34, 0.5);
}

.dark .category-key button {
  background-color: rgba(50, 50, 50, 0.8);
  color: #e0e0e0;
}

/* Animation for category filtering */
.daslist-grid a {
  transition: all 0.3s ease;
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive grid adjustments */
@media (max-width: 1200px) {
  .daslist-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .daslist-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .daslist-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 13%; /* #222222 */
    --foreground: 0 0% 100%; /* #ffffff */

    --card: 0 0% 13%; /* #222222 */
    --card-foreground: 0 0% 100%; /* #ffffff */

    --popover: 0 0% 13%; /* #222222 */
    --popover-foreground: 0 0% 100%; /* #ffffff */

    --primary: 0 0% 100%; /* #ffffff */
    --primary-foreground: 0 0% 13%; /* #222222 */

    --secondary: 0 0% 20%; /* #333333 */
    --secondary-foreground: 0 0% 100%; /* #ffffff */

    --muted: 0 0% 20%; /* #333333 */
    --muted-foreground: 0 0% 80%; /* #cccccc */

    --accent: 60 100% 50%; /* yellow */
    --accent-foreground: 0 0% 13%; /* #222222 */

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%; /* #ffffff */

    --border: 0 0% 20%; /* #333333 */
    --input: 0 0% 40%; /* #666666 for input field */
    --ring: 0 0% 80%; /* #cccccc */
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: hsl(var(--foreground));
    background-color: #E0E0E0; /* Consistent gray background in light mode */
  }

  /* Dark mode styles */
  .dark body {
    background-color: #222222;
    color: #ffffff;
  }

  .dark .bg-\[\#E0E0E0\] {
    background-color: #222222 !important;
  }

  .dark header {
    background-color: #222222;
    border-color: #333333;
  }

  .dark .bg-white {
    background-color: #222222;
  }

  .dark .text-black {
    color: #ffffff;
  }

  .dark .border-gray-300,
  .dark .border-gray-200 {
    border-color: #333333;
  }

  .dark .hover\:bg-gray-200:hover {
    background-color: #333333;
  }

  /* Dark mode menu */
  .dark [data-radix-popper-content-wrapper] {
    background-color: #222222 !important;
  }

  .dark .text-gray-500 {
    color: #999999 !important;
  }

  /* Yellow hover effect for user dropdown items in dark mode */
  .dark .user-dropdown .user-menu-item:hover {
    background-color: #ffd700 !important; /* Yellow */
    color: #222222 !important; /* Dark text for contrast */
  }

  /* Individual hover styles for menu items */
  .dark .user-dropdown [data-radix-dropdown-menu-item]:hover {
    background-color: #ffd700 !important; /* Yellow */
    color: #222222 !important; /* Dark text for contrast */
  }

  /* Dark mode sheet (mobile menu) */
  .dark [role="dialog"] {
    background-color: #222222;
  }

  /* Main page elements */
  .dark .main-content {
    background-color: #222222;
  }

  /* Search input in dark mode */
  .dark input[type="text"],
  .dark input[type="search"] {
    background-color: #222222;
    color: white;
    border-color: #444444;
  }

  /* Navigation tabs dark mode */
  .dark .nav-tabs {
    border-color: #333333;
  }

  .dark .nav-tab {
    color: #ffffff;
  }

  /* Dark mode buttons */
  .dark .btn-dark {
    background-color: #333333;
    color: white;
  }

  /* Ensure primary buttons have black text in dark mode for visibility */
  .dark .bg-primary,
  .dark .bg-primary\/90,
  .dark button[class*="bg-primary"] {
    color: #000000 !important;
    font-weight: 500;
  }

  /* Logo in dark mode */
  .dark .logo-container img,
  .dark .logo-container svg {
    filter: brightness(1.2); /* Make logo more visible in dark mode */
  }

  /* Switch control specific for dark mode */
  .dark .switch-control {
    background-color: #ffffff;
  }

  /* Switch styles override */
  .theme-switch[data-state="checked"] {
    background-color: #ffffff;
  }

  .theme-switch[data-state="unchecked"] {
    background-color: #aaaaaa;
  }

  .theme-switch[data-state="checked"] .switch-thumb {
    background-color: #222222;
  }

  .theme-switch[data-state="unchecked"] .switch-thumb {
    background-color: #ffffff;
  }

  .dark .SafeSphere-active-indicator {
    color: #ffffff !important;
    border-color: #333333 !important;
    background-color: #222222 !important;
  }

  /* Search box dark mode */
  .dark [type="search"] {
    background-color: #222222;
    border-color: #444444;
    color: white;
  }

  /* Make sure home page elements are dark in dark mode */
  .dark main {
    background-color: #222222;
  }

  .dark .bg-\[\#E0E0E0\],
  .dark .bg-gray-100,
  .dark .bg-gray-200 {
    background-color: #222222 !important;
  }

  /* Make sure all Card components are proper dark in dark mode */
  .dark .card {
    background-color: #222222;
    border-color: #333333;
  }

  /* Footer elements */
  .dark footer {
    background-color: #222222;
    border-color: #333333;
  }

  /* Cart icon in dark mode */
  .dark .cart-icon {
    color: white;
  }

  /* Animated "Protected" text styles */
  .animated-text {
    display: inline-block;
    position: relative;
    color: #22c55e; /* Green color */
    font-weight: 500;
    letter-spacing: 0.01em;
    text-shadow: 0 0 1px rgba(34, 197, 94, 0.3);
    padding-left: 2px;
    animation: pulse 2s infinite ease-in-out;
  }

  .dark .animated-text {
    color: #4ade80; /* Brighter green in dark mode */
    text-shadow: 0 0 2px rgba(74, 222, 128, 0.4);
  }

  @keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; text-shadow: 0 0 3px rgba(34, 197, 94, 0.6); }
    100% { opacity: 0.8; }
  }

  .dark .animated-text {
    animation: pulseDark 2s infinite ease-in-out;
  }

  @keyframes pulseDark {
    0% { opacity: 0.8; }
    50% { opacity: 1; text-shadow: 0 0 5px rgba(74, 222, 128, 0.7); }
    100% { opacity: 0.8; }
  }

  /* Animated Trust Text styles */
  .animated-trust-text {
    display: block;
    color: #4B5563 !important; /* Dark grey color (gray-600) */
    font-weight: 500;
    letter-spacing: 0.01em;
    animation: fadeIn 0.8s ease-in-out forwards;
  }

  .dark .animated-trust-text {
    color: #6B7280 !important; /* Slightly lighter grey for dark mode (gray-500) */
  }

  .animated-trust-text.fade-out {
    animation: fadeOut 1.5s ease-in-out forwards;
  }

  @keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }

  @keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
  }

  /* Make the search button match dark mode */
  .dark .search-button {
    background-color: #333333;
    color: white;
  }

  /* Menu sections in dark mode */
  .dark h3.section-heading {
    color: #aaaaaa;
  }

  /* Sign in button dark mode */
  .dark .sign-in-button {
    background-color: #333333;
    color: white;
    border-color: #444444;
  }
}