# Robot Positioning System Setup Guide

## Overview
This document explains the new database-driven robot positioning system that provides:
- **Full screen coverage**: <PERSON>wo<PERSON> can appear anywhere on the entire screen
- **User-specific algorithms**: Each user gets a unique positioning algorithm 
- **1200+ positions per algorithm**: Enough for 1 hour of guessing every 3 seconds
- **Hourly rotation**: Every hour, algorithms shuffle between users in the database
- **Database-driven**: All positioning is managed through the database

## Database Tables Added

### 1. `robot_positioning_algorithms`
Stores positioning algorithms with 1200+ unique positions for robot placement.

**Columns:**
- `id` (serial, primary key)
- `name` (text) - Human-readable name for the algorithm
- `positions` (jsonb) - Array of {x, y} position objects (1200+ positions)
- `screen_width` (integer, default 1920) - Target screen width for positions
- `screen_height` (integer, default 1080) - Target screen height for positions
- `total_positions` (integer) - Number of positions in the algorithm (should be 1200+)
- `is_active` (boolean, default true) - Whether this algorithm is available for assignment
- `created_at` (timestamp)
- `updated_at` (timestamp)

### 2. `user_algorithm_assignments`
Tracks which algorithm each user is currently assigned to, rotated hourly.

**Columns:**
- `id` (serial, primary key)
- `user_id` (integer, foreign key to users.id)
- `algorithm_id` (integer, foreign key to robot_positioning_algorithms.id)
- `current_position_index` (integer, default 0) - Current position index in the algorithm sequence
- `assigned_at` (timestamp)
- `expires_at` (timestamp) - When this assignment expires (1 hour from assignment)
- `is_active` (boolean, default true)

### 3. `algorithm_rotation_log`
Logs algorithm rotation events for monitoring and debugging.

**Columns:**
- `id` (serial, primary key)
- `rotation_time` (timestamp)
- `total_users` (integer) - Number of users that were reassigned
- `total_algorithms` (integer) - Number of algorithms available
- `rotation_details` (jsonb) - JSON object with user->algorithm mapping details
- `created_at` (timestamp)

## Files Updated

### ✅ Database Schema Files
1. **`sowsad/migrations/0000_tidy_white_tiger.sql`** - Main migration file updated
2. **`sowsad/shared/schema.ts`** - TypeScript schema definitions updated
3. **`sowsad/unified_schema.sql`** - Unified schema file updated

### ✅ Backend Implementation
1. **`sowsad/server/services/robot-positioning.ts`** - Core positioning service
2. **`sowsad/server/routes/robot-positioning.ts`** - API endpoints
3. **`sowsad/server/services/algorithm-scheduler.ts`** - Hourly rotation scheduler
4. **`sowsad/server/routes.ts`** - Route registration

### ✅ Frontend Implementation
1. **`sowsad/client/src/services/robot-positioning.ts`** - Frontend API client
2. **`sowsad/client/src/components/robot-overlay.tsx`** - Updated to use database positioning

## Manual Supabase Setup Required

Since the automated migration had issues, you'll need to manually run the SQL in Supabase:

### Option 1: Run the Complete Unified Schema
Execute the entire `sowsad/unified_schema.sql` file in Supabase SQL Editor. This will:
- Create all tables (including the new robot positioning tables)
- Set up all foreign keys and indexes
- Add sample data

### Option 2: Add Only Robot Positioning Tables
If you prefer to add only the new tables to your existing database, run this SQL:

```sql
-- Create robot positioning algorithms table
CREATE TABLE IF NOT EXISTS "robot_positioning_algorithms" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"positions" jsonb NOT NULL,
	"screen_width" integer DEFAULT 1920 NOT NULL,
	"screen_height" integer DEFAULT 1080 NOT NULL,
	"total_positions" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);

-- Create user algorithm assignments table
CREATE TABLE IF NOT EXISTS "user_algorithm_assignments" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"algorithm_id" integer NOT NULL,
	"current_position_index" integer DEFAULT 0 NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL
);

-- Create algorithm rotation log table
CREATE TABLE IF NOT EXISTS "algorithm_rotation_log" (
	"id" serial PRIMARY KEY NOT NULL,
	"rotation_time" timestamp DEFAULT now() NOT NULL,
	"total_users" integer NOT NULL,
	"total_algorithms" integer NOT NULL,
	"rotation_details" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "user_algorithm_assignments" ADD CONSTRAINT "user_algorithm_assignments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "user_algorithm_assignments" ADD CONSTRAINT "user_algorithm_assignments_algorithm_id_robot_positioning_algorithms_id_fk" FOREIGN KEY ("algorithm_id") REFERENCES "robot_positioning_algorithms"("id") ON DELETE no action ON UPDATE no action;

-- Create indexes
CREATE INDEX IF NOT EXISTS "idx_robot_algorithms_active" ON "robot_positioning_algorithms" ("is_active");
CREATE INDEX IF NOT EXISTS "idx_user_assignments_user_active" ON "user_algorithm_assignments" ("user_id", "is_active");
CREATE INDEX IF NOT EXISTS "idx_user_assignments_expires" ON "user_algorithm_assignments" ("expires_at");
CREATE INDEX IF NOT EXISTS "idx_rotation_log_time" ON "algorithm_rotation_log" ("rotation_time");
```

## How It Works

1. **Server Startup**: Algorithm scheduler initializes and creates 10 different algorithms with 1200+ positions each
2. **User Assignment**: When a user first activates Daswos, they get assigned a random algorithm
3. **Position Retrieval**: Each time Daswos needs to move, the frontend calls `/api/robot-positioning/next-position`
4. **Position Tracking**: The system tracks which position in the algorithm sequence the user is on
5. **Hourly Rotation**: Every hour, all users get reassigned to different algorithms
6. **Full Screen Coverage**: Positions are generated across the entire screen with safe margins

## API Endpoints

- `GET /api/robot-positioning/next-position` - Get next position for current user
- `GET /api/robot-positioning/user-algorithm` - Get current algorithm assignment
- `POST /api/robot-positioning/ensure-assignment` - Ensure user has algorithm assignment
- `GET /api/robot-positioning/stats` - Get algorithm statistics (admin only)
- `POST /api/robot-positioning/rotate` - Manually trigger rotation (admin only)

## Benefits

1. **True Randomness**: No more predictable patterns or weighted zones
2. **Fairness**: Every user gets the same quality algorithm with full screen coverage
3. **Scalability**: Can support unlimited users with rotating algorithms
4. **Monitoring**: Full logging and statistics for debugging
5. **Performance**: Database-driven with proper indexing for fast lookups

## Next Steps

1. Run the SQL in Supabase to create the tables
2. Restart the server to initialize the algorithm scheduler
3. Test the positioning system with multiple users
4. Monitor the hourly rotations in the logs
