CREATE TABLE "ai_shopper_recommendations" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"product_id" integer NOT NULL,
	"reason" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"confidence" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"purchased_at" timestamp,
	"rejected_reason" text
);
--> statement-breakpoint
CREATE TABLE "app_settings" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" text NOT NULL,
	"value" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "app_settings_key_unique" UNIQUE("key")
);
--> statement-breakpoint
CREATE TABLE "bulk_buy_requests" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"product_id" integer,
	"request_type" text NOT NULL,
	"quantity" integer,
	"max_budget" integer,
	"special_requirements" text,
	"preferred_delivery_date" timestamp,
	"status" text DEFAULT 'new' NOT NULL,
	"assigned_agent_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "cart_items" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"product_id" integer NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"added_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"source" text DEFAULT 'manual' NOT NULL,
	"recommendation_id" integer
);
--> statement-breakpoint
CREATE TABLE "categories" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"parent_id" integer,
	"level" integer DEFAULT 0 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "category_closure" (
	"ancestor_id" integer NOT NULL,
	"descendant_id" integer NOT NULL,
	"depth" integer NOT NULL,
	CONSTRAINT "category_closure_ancestor_id_descendant_id_pk" PRIMARY KEY("ancestor_id","descendant_id")
);
--> statement-breakpoint
CREATE TABLE "collaborative_collaborators" (
	"id" serial PRIMARY KEY NOT NULL,
	"search_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"role" text DEFAULT 'collaborator' NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"joined_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "collaborative_resources" (
	"id" serial PRIMARY KEY NOT NULL,
	"search_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"source_url" text,
	"source_type" text DEFAULT 'website' NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL,
	"requires_permission" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "collaborative_searches" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"topic" text NOT NULL,
	"tags" text[] NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "daswos_coins_transactions" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"amount" integer NOT NULL,
	"type" text NOT NULL,
	"description" text NOT NULL,
	"status" text DEFAULT 'completed' NOT NULL,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"related_order_id" integer,
	"related_split_buy_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "daswos_ai_chat_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"chat_id" integer NOT NULL,
	"role" text NOT NULL,
	"content" text NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb DEFAULT '{}'::jsonb
);
--> statement-breakpoint
CREATE TABLE "daswos_ai_chats" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"title" text DEFAULT 'New Chat' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"is_archived" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "daswos_ai_sources" (
	"id" serial PRIMARY KEY NOT NULL,
	"message_id" integer NOT NULL,
	"source_type" text NOT NULL,
	"source_id" integer,
	"source_url" text,
	"source_name" text NOT NULL,
	"relevance_score" integer DEFAULT 0 NOT NULL,
	"excerpt" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "family_invitation_codes" (
	"id" serial PRIMARY KEY NOT NULL,
	"code" text NOT NULL,
	"owner_user_id" integer NOT NULL,
	"email" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_used" boolean DEFAULT false NOT NULL,
	"used_by_user_id" integer,
	CONSTRAINT "family_invitation_codes_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "information_content" (
	"id" serial PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"summary" text NOT NULL,
	"source_url" text NOT NULL,
	"source_name" text NOT NULL,
	"source_verified" boolean DEFAULT false NOT NULL,
	"source_type" text DEFAULT 'website' NOT NULL,
	"trust_score" integer NOT NULL,
	"category" text NOT NULL,
	"tags" text[] NOT NULL,
	"image_url" text,
	"verified_since" text,
	"warning" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "order_items" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_id" integer NOT NULL,
	"product_id" integer NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"price_at_purchase" integer NOT NULL,
	"item_name_snapshot" text NOT NULL,
	"split_buy_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "orders" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"order_date" timestamp DEFAULT now() NOT NULL,
	"total_amount" integer NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"shipping_address" text NOT NULL,
	"billing_address" text NOT NULL,
	"payment_method" text NOT NULL,
	"payment_reference" text,
	"notes" text,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "products" (
	"id" serial PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"price" integer NOT NULL,
	"image_url" text NOT NULL,
	"seller_id" integer NOT NULL,
	"seller_name" text NOT NULL,
	"seller_verified" boolean DEFAULT false NOT NULL,
	"seller_type" text DEFAULT 'merchant' NOT NULL,
	"trust_score" integer NOT NULL,
	"identity_verified" boolean DEFAULT false NOT NULL,
	"identity_verification_status" text DEFAULT 'none' NOT NULL,
	"tags" text[] NOT NULL,
	"shipping" text NOT NULL,
	"original_price" integer,
	"discount" integer,
	"verified_since" text,
	"warning" text,
	"is_bulk_buy" boolean DEFAULT false NOT NULL,
	"bulk_minimum_quantity" integer,
	"bulk_discount_rate" integer,
	"image_description" text,
	"category_id" integer,
	"ai_attributes" jsonb DEFAULT '{}',
	"search_vector" text,
	"status" text DEFAULT 'active' NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"sold_quantity" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "purchases" (
	"id" serial PRIMARY KEY NOT NULL,
	"buyer_id" integer NOT NULL,
	"seller_id" integer NOT NULL,
	"product_id" integer NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"total_price" integer NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"transaction_id" integer,
	"purchased_at" timestamp DEFAULT now() NOT NULL,
	"received_at" timestamp,
	"rating" integer,
	"review_comment" text,
	"rated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "resource_permission_requests" (
	"id" serial PRIMARY KEY NOT NULL,
	"resource_id" integer NOT NULL,
	"requester_id" integer NOT NULL,
	"message" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "search_queries" (
	"id" serial PRIMARY KEY NOT NULL,
	"query" text NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"sphere" text NOT NULL,
	"content_type" text DEFAULT 'products' NOT NULL,
	"filters" jsonb,
	"user_id" integer,
	"super_safe_enabled" boolean DEFAULT false,
	"super_safe_settings" jsonb
);
--> statement-breakpoint
CREATE TABLE "seller_verifications" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"type" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"submitted_at" timestamp DEFAULT now() NOT NULL,
	"processed_at" timestamp,
	"deposit_amount" integer,
	"comments" text,
	"document_urls" text[]
);
--> statement-breakpoint

CREATE TABLE "split_buy_participants" (
	"id" serial PRIMARY KEY NOT NULL,
	"split_buy_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"quantity_committed" integer DEFAULT 1 NOT NULL,
	"payment_status" text DEFAULT 'pending' NOT NULL,
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "split_buys" (
	"id" serial PRIMARY KEY NOT NULL,
	"product_id" integer NOT NULL,
	"initiator_user_id" integer NOT NULL,
	"target_quantity" integer NOT NULL,
	"current_quantity" integer DEFAULT 0 NOT NULL,
	"price_per_unit" integer NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"expires_at" timestamp,
	"description" text,
	"min_participants" integer DEFAULT 2,
	"max_participants" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_dasbar_preferences" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"items" jsonb NOT NULL,
	"max_visible_items" integer DEFAULT 4 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_payment_methods" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"stripe_customer_id" text NOT NULL,
	"stripe_payment_method_id" text NOT NULL,
	"last4" text NOT NULL,
	"card_type" text NOT NULL,
	"expiry_month" integer NOT NULL,
	"expiry_year" integer NOT NULL,
	"is_default" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_sessions" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"session_token" text NOT NULL,
	"device_info" jsonb DEFAULT '{}'::jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"last_active" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	CONSTRAINT "user_sessions_session_token_unique" UNIQUE("session_token")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" text NOT NULL,
	"password" text NOT NULL,
	"email" text NOT NULL,
	"full_name" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"is_seller" boolean DEFAULT false NOT NULL,
	"is_admin" boolean DEFAULT false NOT NULL,
	"avatar" text,
	"has_subscription" boolean DEFAULT false NOT NULL,
	"subscription_type" text,
	"subscription_expires_at" timestamp,
	"is_family_owner" boolean DEFAULT false NOT NULL,
	"family_owner_id" integer,
	"parent_account_id" integer,
	"is_child_account" boolean DEFAULT false NOT NULL,
	"super_safe_mode" boolean DEFAULT false NOT NULL,
	"super_safe_settings" jsonb DEFAULT '{"blockGambling":true,"blockAdultContent":true,"blockOpenSphere":false}'::jsonb,
	"safe_sphere_active" boolean DEFAULT false NOT NULL,
	"ai_shopper_enabled" boolean DEFAULT false NOT NULL,
	"ai_shopper_settings" jsonb DEFAULT '{"autoPurchase":false,"autoPaymentEnabled":false,"confidenceThreshold":0.85,"budgetLimit":5000,"maxTransactionLimit":10000,"preferredCategories":[],"avoidTags":[],"minimumTrustScore":85,"purchaseMode":"refined","maxPricePerItem":5000,"maxCoinsPerItem":50,"maxCoinsPerDay":100,"maxCoinsOverall":1000,"purchaseFrequency":{"hourly":1,"daily":5,"monthly":50}}'::jsonb,
	"daswos_coins_balance" integer DEFAULT 0 NOT NULL,

	-- Multi-wallet support (up to 5 wallets per user)
	"primary_wallet_id" text, -- Primary wallet ID for this user
	"wallet_ids" text[] DEFAULT '{}', -- Array of connected wallet IDs (max 5)
	"wallet_nicknames" jsonb DEFAULT '{}'::jsonb, -- User-defined nicknames for wallets
	"active_wallet_id" text, -- Currently active wallet ID

	"identity_verified" boolean DEFAULT false NOT NULL,
	"identity_verification_status" text DEFAULT 'none' NOT NULL,
	"identity_verification_submitted_at" timestamp,
	"identity_verification_approved_at" timestamp,
	"identity_verification_data" jsonb DEFAULT '{}'::jsonb,
	"trust_score" integer DEFAULT 30 NOT NULL,
	"business_name" text,
	"business_type" text DEFAULT 'individual' NOT NULL,
	"business_address" text,
	"contact_phone" text,
	"tax_id" text,
	"website" text,
	"year_established" integer,
	"business_description" text,
	"profile_image_url" text,
	"document_urls" text[],
	CONSTRAINT "users_username_unique" UNIQUE("username"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
-- Create explicit unique indexes for username and email (case-insensitive)
CREATE UNIQUE INDEX "idx_users_username_unique_ci" ON "users" (LOWER("username"));
--> statement-breakpoint
CREATE UNIQUE INDEX "idx_users_email_unique_ci" ON "users" (LOWER("email"));
--> statement-breakpoint
ALTER TABLE "category_closure" ADD CONSTRAINT "category_closure_ancestor_id_categories_id_fk" FOREIGN KEY ("ancestor_id") REFERENCES "public"."categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "category_closure" ADD CONSTRAINT "category_closure_descendant_id_categories_id_fk" FOREIGN KEY ("descendant_id") REFERENCES "public"."categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "family_invitation_codes" ADD CONSTRAINT "family_invitation_codes_owner_user_id_users_id_fk" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "family_invitation_codes" ADD CONSTRAINT "family_invitation_codes_used_by_user_id_users_id_fk" FOREIGN KEY ("used_by_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "products_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cart_items" ADD CONSTRAINT "cart_items_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cart_items" ADD CONSTRAINT "cart_items_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "purchases_buyer_id_users_id_fk" FOREIGN KEY ("buyer_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "purchases_seller_id_users_id_fk" FOREIGN KEY ("seller_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "purchases_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "purchases_transaction_id_daswos_coins_transactions_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "public"."daswos_coins_transactions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_dasbar_preferences" ADD CONSTRAINT "user_dasbar_preferences_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_sessions" ADD CONSTRAINT "user_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint

-- Add constraints for identity verification and business fields
-- Note: identity_verification_status constraint removed - handled by database trigger that only allows values starting with 'app' or 'none'
ALTER TABLE "users" ADD CONSTRAINT "check_trust_score_range" CHECK ("trust_score" >= 0 AND "trust_score" <= 100);--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "check_business_type" CHECK ("business_type" IN ('individual', 'business', 'corporation', 'nonprofit', 'partnership'));--> statement-breakpoint

-- Add constraints for multi-wallet support
ALTER TABLE "users" ADD CONSTRAINT "check_wallet_ids_limit" CHECK (array_length("wallet_ids", 1) <= 5 OR "wallet_ids" IS NULL);--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "check_primary_wallet_in_array" CHECK ("primary_wallet_id" IS NULL OR "primary_wallet_id" = ANY("wallet_ids"));--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "check_active_wallet_in_array" CHECK ("active_wallet_id" IS NULL OR "active_wallet_id" = ANY("wallet_ids"));--> statement-breakpoint

-- Create indexes for performance
CREATE INDEX "idx_users_trust_score" ON "users"("trust_score");--> statement-breakpoint
CREATE INDEX "idx_users_identity_verified" ON "users"("identity_verified");--> statement-breakpoint
CREATE INDEX "idx_users_identity_verification_status" ON "users"("identity_verification_status");--> statement-breakpoint
CREATE INDEX "idx_users_business_type" ON "users"("business_type");--> statement-breakpoint

-- Create indexes for multi-wallet support
CREATE INDEX "idx_users_primary_wallet_id" ON "users"("primary_wallet_id");--> statement-breakpoint
CREATE INDEX "idx_users_active_wallet_id" ON "users"("active_wallet_id");--> statement-breakpoint
CREATE INDEX "idx_users_wallet_ids" ON "users" USING GIN ("wallet_ids");--> statement-breakpoint


-- Add unique constraint for category names
ALTER TABLE "categories" ADD CONSTRAINT "categories_name_unique" UNIQUE("name");--> statement-breakpoint
-- Add missing user history tables
CREATE TABLE "user_purchase_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"product_id" integer NOT NULL,
	"category_id" integer,
	"purchase_date" timestamp DEFAULT now() NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"price" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_search_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"search_query" text NOT NULL,
	"search_date" timestamp DEFAULT now() NOT NULL,
	"category_id" integer,
	"clicked_product_id" integer
);
--> statement-breakpoint
CREATE TABLE "user_product_preferences" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"category_id" integer NOT NULL,
	"preference_score" integer DEFAULT 0 NOT NULL,
	"last_updated" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "user_product_preferences_user_id_category_id_unique" UNIQUE("user_id","category_id")
);
--> statement-breakpoint

-- Add foreign key constraints for user history tables
ALTER TABLE "user_purchase_history" ADD CONSTRAINT "user_purchase_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_purchase_history" ADD CONSTRAINT "user_purchase_history_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_purchase_history" ADD CONSTRAINT "user_purchase_history_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_search_history" ADD CONSTRAINT "user_search_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_search_history" ADD CONSTRAINT "user_search_history_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_search_history" ADD CONSTRAINT "user_search_history_clicked_product_id_products_id_fk" FOREIGN KEY ("clicked_product_id") REFERENCES "public"."products"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_product_preferences" ADD CONSTRAINT "user_product_preferences_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_product_preferences" ADD CONSTRAINT "user_product_preferences_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint

-- Add comment to document DasWos coins balance calculation
COMMENT ON TABLE "daswos_coins_transactions" IS 'DasWos coins transactions - user balance is calculated by summing transactions (add for purchase/reward/refund/admin, subtract for spend)';

-- User Subscriptions table (for Stripe subscription management)
CREATE TABLE "user_subscriptions" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"stripe_customer_id" text,
	"stripe_subscription_id" text,
	"subscription_type" text NOT NULL,
	"billing_cycle" text NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"current_period_start" timestamp,
	"current_period_end" timestamp,
	"cancel_at_period_end" boolean DEFAULT false,
	"canceled_at" timestamp,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint

-- Add foreign key constraint for user_subscriptions
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "user_subscriptions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint

-- Add indexes for user_subscriptions performance
CREATE INDEX "idx_user_subscriptions_user_id" ON "user_subscriptions"("user_id");--> statement-breakpoint
CREATE INDEX "idx_user_subscriptions_stripe_subscription_id" ON "user_subscriptions"("stripe_subscription_id");--> statement-breakpoint
CREATE INDEX "idx_user_subscriptions_status" ON "user_subscriptions"("status");--> statement-breakpoint

-- Add constraints for subscription fields
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "check_subscription_type" CHECK ("subscription_type" IN ('limited', 'unlimited', 'premium', 'enterprise'));--> statement-breakpoint
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "check_billing_cycle" CHECK ("billing_cycle" IN ('monthly', 'annual'));--> statement-breakpoint
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "check_subscription_status" CHECK ("status" IN ('active', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete', 'incomplete_expired'));--> statement-breakpoint

-- Additional performance indexes for key application features
CREATE INDEX "idx_products_seller_id" ON "products"("seller_id");--> statement-breakpoint
CREATE INDEX "idx_products_status" ON "products"("status");--> statement-breakpoint
CREATE INDEX "idx_products_seller_verified" ON "products"("seller_verified");--> statement-breakpoint
CREATE INDEX "idx_products_trust_score" ON "products"("trust_score");--> statement-breakpoint
CREATE INDEX "idx_purchases_buyer_id" ON "purchases"("buyer_id");--> statement-breakpoint
CREATE INDEX "idx_purchases_seller_id" ON "purchases"("seller_id");--> statement-breakpoint
CREATE INDEX "idx_purchases_status" ON "purchases"("status");--> statement-breakpoint
CREATE INDEX "idx_purchases_rating" ON "purchases"("rating");--> statement-breakpoint
CREATE INDEX "idx_daswos_coins_transactions_user_id" ON "daswos_coins_transactions"("user_id");--> statement-breakpoint
CREATE INDEX "idx_daswos_coins_transactions_type" ON "daswos_coins_transactions"("type");--> statement-breakpoint

-- Add constraints for key application logic
ALTER TABLE "products" ADD CONSTRAINT "check_product_status" CHECK ("status" IN ('draft', 'active', 'sold', 'inactive'));--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "check_product_quantity" CHECK ("quantity" >= 0);--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "check_product_sold_quantity" CHECK ("sold_quantity" >= 0);--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "check_product_price" CHECK ("price" > 0);--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "check_purchase_status" CHECK ("status" IN ('pending', 'confirmed', 'shipped', 'delivered', 'received', 'cancelled', 'refunded'));--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "check_purchase_rating" CHECK ("rating" IS NULL OR ("rating" >= 1 AND "rating" <= 5));--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "check_purchase_quantity" CHECK ("quantity" > 0);--> statement-breakpoint
ALTER TABLE "purchases" ADD CONSTRAINT "check_purchase_total_price" CHECK ("total_price" > 0);--> statement-breakpoint
ALTER TABLE "daswos_coins_transactions" ADD CONSTRAINT "check_transaction_type" CHECK ("type" IN ('purchase', 'spend', 'reward', 'refund', 'admin'));--> statement-breakpoint
ALTER TABLE "daswos_coins_transactions" ADD CONSTRAINT "check_transaction_status" CHECK ("status" IN ('pending', 'completed', 'failed', 'cancelled'));--> statement-breakpoint

-- Database trigger to sanitize identity_verification_status values
CREATE OR REPLACE FUNCTION sanitize_identity_verification_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Sanitize identity_verification_status: only allow values starting with 'app'
    -- Everything else becomes 'none'
    IF NEW.identity_verification_status IS NOT NULL THEN
        IF NOT (NEW.identity_verification_status LIKE 'app%') THEN
            NEW.identity_verification_status := 'none';
        END IF;
    ELSE
        -- If NULL, set to 'none'
        NEW.identity_verification_status := 'none';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Database trigger to automatically update trust score when identity verification changes
CREATE OR REPLACE FUNCTION update_trust_score_on_identity_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if identity verification status changed to fully approved
    -- Only add points when BOTH identity_verified = true AND identity_verification_status starts with 'app'

    -- Calculate old and new verification states
    -- Now ANY value starting with 'app' counts as fully verified
    DECLARE
        old_fully_verified BOOLEAN := (OLD.identity_verified = true AND OLD.identity_verification_status LIKE 'app%');
        new_fully_verified BOOLEAN := (NEW.identity_verified = true AND NEW.identity_verification_status LIKE 'app%');
    BEGIN
        -- If verification state changed from not fully verified to fully verified
        IF old_fully_verified = false AND new_fully_verified = true THEN
            NEW.trust_score = LEAST(100, NEW.trust_score + 30);
            RAISE NOTICE 'Identity fully verified for user %: trust score increased by 30 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;

        -- If verification state changed from fully verified to not fully verified
        ELSIF old_fully_verified = true AND new_fully_verified = false THEN
            NEW.trust_score = GREATEST(0, NEW.trust_score - 30);
            RAISE NOTICE 'Identity verification removed for user %: trust score decreased by 30 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;
        END IF;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the sanitization trigger (runs first on INSERT and UPDATE)
CREATE TRIGGER sanitize_identity_verification_status_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION sanitize_identity_verification_status();

-- Create the trust score trigger (runs after sanitization on UPDATE only)
CREATE TRIGGER trigger_update_trust_score_on_identity_change
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_trust_score_on_identity_change();

-- Note: One-time fix removed to prevent automatic trust score grants
-- All identity verifications must now be manually approved by admins
-- The database trigger will automatically handle trust score updates when approved

-- Seed categories with comprehensive list
INSERT INTO "categories" ("name", "description", "level") VALUES
('Electronics', 'Electronic devices and gadgets', 0),
('Fashion & Clothing', 'Clothing, shoes, and accessories', 0),
('Home & Garden', 'Home improvement, furniture, and garden items', 0),
('Sports & Outdoors', 'Sports equipment and outdoor gear', 0),
('Health & Beauty', 'Health, beauty, and personal care products', 0),
('Toys & Games', 'Toys, games, and entertainment', 0),
('Books & Media', 'Books, movies, music, and digital media', 0),
('Automotive', 'Car parts, accessories, and automotive tools', 0),
('Collectibles & Art', 'Collectible items, art, and antiques', 0),
('Business & Industrial', 'Business equipment and industrial supplies', 0),
('Pet Supplies', 'Pet food, toys, and accessories', 0),
('Baby & Kids', 'Baby gear, kids clothing, and children items', 0),
('Jewelry & Watches', 'Jewelry, watches, and precious items', 0),
('Musical Instruments', 'Instruments, audio equipment, and music gear', 0),
('Crafts & Hobbies', 'Craft supplies, hobby materials, and DIY items', 0),
('Food & Beverages', 'Food items, beverages, and gourmet products', 0),
('Travel & Luggage', 'Travel gear, luggage, and vacation items', 0),
('Office Supplies', 'Office equipment, stationery, and supplies', 0),
('Other', 'Items that don''t fit other categories', 0)
ON CONFLICT ("name") DO NOTHING;

-- Robot Positioning Algorithm Tables
-- Added for database-driven robot positioning with hourly rotation

-- Create robot positioning algorithms table
CREATE TABLE IF NOT EXISTS "robot_positioning_algorithms" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"positions" jsonb NOT NULL,
	"screen_width" integer DEFAULT 1920 NOT NULL,
	"screen_height" integer DEFAULT 1080 NOT NULL,
	"total_positions" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint

-- Create user algorithm assignments table
CREATE TABLE IF NOT EXISTS "user_algorithm_assignments" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"algorithm_id" integer NOT NULL,
	"current_position_index" integer DEFAULT 0 NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint

-- Create algorithm rotation log table
CREATE TABLE IF NOT EXISTS "algorithm_rotation_log" (
	"id" serial PRIMARY KEY NOT NULL,
	"rotation_time" timestamp DEFAULT now() NOT NULL,
	"total_users" integer NOT NULL,
	"total_algorithms" integer NOT NULL,
	"rotation_details" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint

-- Add foreign key constraints for robot positioning tables
-- Note: user_id constraint removed since it can now be session IDs that don't exist in users table
ALTER TABLE "user_algorithm_assignments" ADD CONSTRAINT "user_algorithm_assignments_algorithm_id_robot_positioning_algorithms_id_fk" FOREIGN KEY ("algorithm_id") REFERENCES "public"."robot_positioning_algorithms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint

-- Create indexes for robot positioning tables
CREATE INDEX IF NOT EXISTS "idx_robot_algorithms_active" ON "robot_positioning_algorithms" ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_assignments_user_active" ON "user_algorithm_assignments" ("user_id", "is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_assignments_expires" ON "user_algorithm_assignments" ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_rotation_log_time" ON "algorithm_rotation_log" ("rotation_time");--> statement-breakpoint

-- Add comments for robot positioning tables
COMMENT ON TABLE "robot_positioning_algorithms" IS 'Stores positioning algorithms with 1200+ unique positions for robot placement';--> statement-breakpoint
COMMENT ON TABLE "user_algorithm_assignments" IS 'Tracks which algorithm each user is currently assigned to, rotated hourly';--> statement-breakpoint
COMMENT ON TABLE "algorithm_rotation_log" IS 'Logs algorithm rotation events for monitoring and debugging';--> statement-breakpoint
COMMENT ON COLUMN "robot_positioning_algorithms"."positions" IS 'JSONB array of {x, y} position objects';--> statement-breakpoint
COMMENT ON COLUMN "robot_positioning_algorithms"."total_positions" IS 'Number of positions in the algorithm (should be 1200+)';--> statement-breakpoint
COMMENT ON COLUMN "user_algorithm_assignments"."user_id" IS 'User identifier - can be numeric user ID for authenticated users or session_<sessionId> for guest users';--> statement-breakpoint
COMMENT ON COLUMN "user_algorithm_assignments"."current_position_index" IS 'Current position index in the algorithm sequence';--> statement-breakpoint
COMMENT ON COLUMN "user_algorithm_assignments"."expires_at" IS 'When this assignment expires (1 hour from assignment)';--> statement-breakpoint
COMMENT ON COLUMN "algorithm_rotation_log"."rotation_details" IS 'JSON object with user->algorithm mapping details';