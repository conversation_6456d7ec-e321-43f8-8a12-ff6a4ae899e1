import React, { useState, useCallback, useRef, useEffect } from 'react';
import RobotAnimation, { RobotAnimationRef } from './robot-animation';
import RobotControls from './robot-controls';
import FullscreenProductDisplay from './fullscreen-product-display';
import { RobotState } from '@/hooks/use-robot-animation';
import { useRobotContext } from '@/contexts/robot-context';
import { useToast } from '@/hooks/use-toast';
import { Button } from './ui/button';
import { Search, X } from 'lucide-react';

interface RobotOverlayProps {
  className?: string;
}

const RobotOverlay: React.FC<RobotOverlayProps> = ({ className = '' }) => {
  const [robotState, setRobotState] = useState<RobotState>('idle');
  const [robotScale, setRobotScale] = useState<number>(0.3); // Default to 30% size
  const [isRobotActive, setIsRobotActive] = useState<boolean>(false);
  const [isRobotGlidingAway, setIsRobotGlidingAway] = useState<boolean>(false);
  const [showRobotControls, setShowRobotControls] = useState<boolean>(true);
  const [isFullScreenMode, setIsFullScreenMode] = useState<boolean>(false);

  const [showProductDisplay, setShowProductDisplay] = useState<boolean>(false);
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('');
  const [showSearchBar, setShowSearchBar] = useState<boolean>(false);
  const [showInformationPopup, setShowInformationPopup] = useState<boolean>(false);
  const [informationResults, setInformationResults] = useState<any[]>([]);
  const [isProductPopupOpen, setIsProductPopupOpen] = useState<boolean>(false);
  const [robotPosition, setRobotPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [lightsOn, setLightsOn] = useState<boolean>(false);
  const robotAnimationRef = useRef<RobotAnimationRef>(null);

  // Guessing game state
  const [isGuessMode, setIsGuessMode] = useState<boolean>(false);
  const [userGuess, setUserGuess] = useState<{ x: number; y: number } | null>(null);
  const [lastRobotPosition, setLastRobotPosition] = useState<{ x: number; y: number } | null>(null);
  const [showGuessResult, setShowGuessResult] = useState<boolean>(false);
  const [guessWasCorrect, setGuessWasCorrect] = useState<boolean>(false);
  const [guessDistance, setGuessDistance] = useState<number>(0);

  // Position tracking to prevent duplicates and ensure consistency
  const [currentRobotPosition, setCurrentRobotPosition] = useState<{ x: number; y: number } | null>(null);
  const [nextRobotPosition, setNextRobotPosition] = useState<{ x: number; y: number } | null>(null);

  // Generate random position for robot when lights turn on (with duplicate prevention)
  const generateRandomRobotPosition = useCallback((avoidPosition?: { x: number; y: number } | null) => {
    // Define safe zones to avoid UI elements
    const safeMargin = 120; // pixels from edges
    const minX = safeMargin;
    const maxX = window.innerWidth - safeMargin;
    const minY = safeMargin;
    const maxY = window.innerHeight - safeMargin;

    // Enhanced random algorithm with weighted zones
    // Create preference for certain areas (corners, center, etc.)
    const zones = [
      // Bottom left corner (25% weight)
      { x: minX, y: maxY * 0.7, weight: 0.25 },
      // Bottom right corner (20% weight)
      { x: maxX * 0.8, y: maxY * 0.7, weight: 0.20 },
      // Center area (30% weight)
      { x: window.innerWidth * 0.5, y: window.innerHeight * 0.5, weight: 0.30 },
      // Top areas (15% weight)
      { x: window.innerWidth * 0.3, y: minY + 50, weight: 0.15 },
      // Random anywhere (10% weight)
      { x: Math.random() * (maxX - minX) + minX, y: Math.random() * (maxY - minY) + minY, weight: 0.10 }
    ];

    let attempts = 0;
    let finalX, finalY;
    const maxAttempts = 10; // Prevent infinite loops
    const minDistance = 150; // Minimum distance from previous position

    do {
      // Select zone based on weighted random
      const random = Math.random();
      let cumulativeWeight = 0;
      let selectedZone = zones[0];

      for (const zone of zones) {
        cumulativeWeight += zone.weight;
        if (random <= cumulativeWeight) {
          selectedZone = zone;
          break;
        }
      }

      // Add some randomness around the selected zone
      const variance = 80; // pixels of variance around the zone center
      finalX = Math.max(minX, Math.min(maxX, selectedZone.x + (Math.random() - 0.5) * variance));
      finalY = Math.max(minY, Math.min(maxY, selectedZone.y + (Math.random() - 0.5) * variance));

      attempts++;

      // Check distance from previous position if provided
      if (avoidPosition) {
        const distance = Math.sqrt(
          Math.pow(finalX - avoidPosition.x, 2) + Math.pow(finalY - avoidPosition.y, 2)
        );
        if (distance >= minDistance || attempts >= maxAttempts) {
          break; // Position is far enough or we've tried enough times
        }
      } else {
        break; // No previous position to avoid
      }
    } while (attempts < maxAttempts);

    console.log('🎲 Generated random robot position:', {
      finalX,
      finalY,
      attempts,
      avoidedPosition: avoidPosition,
      distance: avoidPosition ? Math.sqrt(Math.pow(finalX - avoidPosition.x, 2) + Math.pow(finalY - avoidPosition.y, 2)) : null,
      windowSize: { width: window.innerWidth, height: window.innerHeight }
    });

    return { x: finalX, y: finalY };
  }, []);

  // Pre-generate next position to ensure consistency between guessing and normal mode
  const getNextRobotPosition = useCallback(() => {
    if (!nextRobotPosition) {
      // Generate next position, avoiding current position
      const newPosition = generateRandomRobotPosition(currentRobotPosition);
      setNextRobotPosition(newPosition);
      console.log('🎯 Pre-generated next robot position:', newPosition);
      return newPosition;
    }
    return nextRobotPosition;
  }, [nextRobotPosition, currentRobotPosition, generateRandomRobotPosition]);

  // Handle lights state change to reposition robot
  const handleLightsChange = useCallback((newLightsOn: boolean) => {
    console.log('💡 handleLightsChange called:', { newLightsOn, robotActive: isRobotActive, robotRef: !!robotAnimationRef.current, hasGuess: !!userGuess });
    setLightsOn(newLightsOn);

    // Move robot to new random position every time lights are toggled (on OR off) and robot is active
    if (isRobotActive && robotAnimationRef.current) {
      // Use the pre-generated next position to ensure consistency
      const newPosition = getNextRobotPosition();
      console.log('💡 Moving robot to pre-determined position:', newPosition);

      // Use setPosition to move robot without changing center position
      robotAnimationRef.current.setPosition(newPosition.x, newPosition.y);

      robotAnimationRef.current.setRobotScale(robotScale); // Maintain current scale
      setRobotPosition(newPosition);
      setLastRobotPosition(newPosition); // Store for guessing game

      // Update position tracking
      setCurrentRobotPosition(newPosition);
      setNextRobotPosition(null); // Clear next position so a new one will be generated

      console.log('💡 Lights toggled - robot moved to position:', newPosition);

      // Check if user had made a guess - ONLY when lights are turned ON
      if (userGuess && newLightsOn) {
        const distance = Math.sqrt(
          Math.pow(newPosition.x - userGuess.x, 2) +
          Math.pow(newPosition.y - userGuess.y, 2)
        );
        const isCorrect = distance <= 100; // Within 100px is considered correct

        console.log('🎯 Guess check (lights ON):', {
          guess: userGuess,
          actual: newPosition,
          distance: Math.round(distance),
          isCorrect
        });

        setGuessDistance(Math.round(distance));
        setGuessWasCorrect(isCorrect);
        setShowGuessResult(true);

        // Reset guess state
        setUserGuess(null);
        setIsGuessMode(false);

        // Auto-hide result after 8 seconds (longer to see both markers)
        setTimeout(() => {
          setShowGuessResult(false);
          setLastRobotPosition(null); // Clear the actual position marker
        }, 8000);
      } else if (userGuess && !newLightsOn) {
        // If lights are turned OFF and user has a guess, just log but don't trigger game
        console.log('💡 Lights turned OFF - guess game not triggered');
      }
    }
  }, [getNextRobotPosition, isRobotActive, robotScale, userGuess]);

  // Use robot context to share full screen state globally
  const { setIsRobotFullScreen } = useRobotContext();
  const { toast } = useToast();

  // Handle screen click for guessing game
  const handleScreenClick = useCallback((event: React.MouseEvent) => {
    if (isGuessMode && isFullScreenMode) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      console.log('🎯 User made guess at:', { x, y });
      setUserGuess({ x, y });
      setIsGuessMode(false);

      toast({
        title: "Guess Recorded! 🎯",
        description: "Now turn the lights ON to see where Daswos appears!",
        duration: 3000,
      });
    }
  }, [isGuessMode, isFullScreenMode, toast]);

  // Handle guess mode activation (new "?" button functionality)
  const handleGuessMode = useCallback(() => {
    if (!isFullScreenMode) {
      toast({
        title: "Activate Daswos first",
        description: "You need to activate Daswos robot to play the guessing game!",
        variant: "destructive",
      });
      return;
    }

    setIsGuessMode(true);
    setUserGuess(null);
    setShowGuessResult(false);

    // Pre-generate the next position so user is guessing the actual next position
    getNextRobotPosition();

    toast({
      title: "Guessing Game! 🎯",
      description: "Click anywhere on the screen to guess where Daswos will appear when lights are next turned on!",
      duration: 5000,
    });
  }, [isFullScreenMode, toast, getNextRobotPosition]);

  const handleStateChange = useCallback((newState: RobotState) => {
    setRobotState(newState);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState(newState);
    }

    // Show search bar only when search button is clicked (not voice commands)
    if (newState === 'search') {
      setShowSearchBar(true);
    }
  }, []);

  const handleScaleChange = useCallback((newScale: number) => {
    setRobotScale(newScale);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotScale(newScale);
    }
  }, []);

  // DISABLED: Center button interferes with guessing game positioning
  const handleCenter = useCallback(() => {
    // DISABLED: Don't center robot - interferes with guessing game
    console.log('🚫 Center button disabled for guessing game');
    // if (robotAnimationRef.current) {
    //   robotAnimationRef.current.centerRobot();
    // }
  }, []);

  const handleRoll = useCallback(() => {
    // Roll to a random position
    const x = Math.random() * window.innerWidth * 0.6 + window.innerWidth * 0.2;
    const y = Math.random() * window.innerHeight * 0.6 + window.innerHeight * 0.2;

    if (robotAnimationRef.current) {
      robotAnimationRef.current.rollToPosition(x, y);
    }
    setRobotState('roll');
  }, []);

  const handleRobotStateChange = useCallback((state: RobotState) => {
    setRobotState(state);
    // Note: Don't show search bar here - only show when search button is manually clicked
  }, []);

  // Track robot position for hologram placement
  const handleRobotPositionChange = useCallback((x: number, y: number) => {
    setRobotPosition({ x, y });
  }, []);

  const handleDaswosButtonClick = useCallback(() => {
    setIsRobotActive(true);
    setShowRobotControls(true); // Show controls in full screen mode
    setIsFullScreenMode(true); // Start in full screen mode
    setIsRobotFullScreen(true); // Set global context to full screen mode

    // Generate random position for robot when activated
    if (robotAnimationRef.current) {
      const randomPosition = generateRandomRobotPosition();
      // Use setPosition to avoid changing center position for guessing game
      robotAnimationRef.current.setPosition(randomPosition.x, randomPosition.y);
      robotAnimationRef.current.setRobotScale(robotScale); // Use current scale (30% by default)
      // Update robot position state for hologram
      setRobotPosition(randomPosition);
      // Track this as the current position for duplicate prevention
      setCurrentRobotPosition(randomPosition);
      console.log('🤖 Robot activated at random position:', randomPosition);
    }

    // Dispatch robot activated event
    const activatedEvent = new CustomEvent('robotActivated');
    window.dispatchEvent(activatedEvent);

    // Robot activated - no popup needed
  }, [setIsRobotFullScreen, toast, generateRandomRobotPosition]);

  // Listen for global robot activation events
  useEffect(() => {
    const handleActivateRobot = () => {
      handleDaswosButtonClick();
    };

    window.addEventListener('activateRobot', handleActivateRobot);

    return () => {
      window.removeEventListener('activateRobot', handleActivateRobot);
    };
  }, [handleDaswosButtonClick]);

  const handleCloseRobotControls = useCallback(() => {
    setShowRobotControls(false);
  }, []);

  const handleFullScreenToggle = useCallback(() => {
    const newFullScreenMode = !isFullScreenMode;
    setIsFullScreenMode(newFullScreenMode);
    setIsRobotFullScreen(newFullScreenMode); // Update global context

    if (robotAnimationRef.current) {
      if (newFullScreenMode) {
        // Generate random position for full screen mode
        const randomPosition = generateRandomRobotPosition(currentRobotPosition);
        // Use setPosition to avoid changing center position for guessing game
        robotAnimationRef.current.setPosition(randomPosition.x, randomPosition.y);
        // Update robot position state for hologram
        setRobotPosition(randomPosition);
        // Track this as the current position for duplicate prevention
        setCurrentRobotPosition(randomPosition);
        console.log('🤖 Full screen mode - robot moved to random position:', randomPosition);
      } else {
        // Move back to bottom-right for compact mode
        const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
        const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)
        // Use setPosition to avoid changing center position for guessing game
        robotAnimationRef.current.setPosition(targetX, targetY);
        // Update robot position state
        setRobotPosition({ x: targetX, y: targetY });
        // Track this as the current position for duplicate prevention
        setCurrentRobotPosition({ x: targetX, y: targetY });
      }
    }
  }, [isFullScreenMode, setIsRobotFullScreen, generateRandomRobotPosition, currentRobotPosition]);

  const handleGoAway = useCallback(() => {
    setIsRobotGlidingAway(true);
    setIsFullScreenMode(false); // Reset to compact mode
    setIsRobotFullScreen(false); // Reset global context

    if (robotAnimationRef.current) {
      robotAnimationRef.current.glideAway();
    }

    // After the glide animation completes, hide everything
    setTimeout(() => {
      setIsRobotActive(false);
      setIsRobotGlidingAway(false);

      // Dispatch robot deactivated event
      const deactivatedEvent = new CustomEvent('robotDeactivated');
      window.dispatchEvent(deactivatedEvent);
    }, 2000); // 2 seconds for the glide animation
  }, [setIsRobotFullScreen]);

  const handleCloseProductDisplay = useCallback(() => {
    setShowProductDisplay(false);
    setCurrentSearchQuery('');
    // Exit full screen mode when closing the product display
    setIsFullScreenMode(false);
    setIsRobotFullScreen(false);

    // Move robot back to compact position
    if (robotAnimationRef.current) {
      const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
      const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)
      // Use setPosition to avoid changing center position for guessing game
      robotAnimationRef.current.setPosition(targetX, targetY);
    }
  }, [setIsRobotFullScreen]);

  const handleClearSearch = useCallback(() => {
    setCurrentSearchQuery('');
    // Keep full screen mode but clear search to show random products
  }, []);

  const handleInformationSearch = useCallback(async (query: string) => {
    try {
      const response = await fetch(`/api/information?q=${encodeURIComponent(query)}&sphere=opensphere`);
      if (!response.ok) throw new Error('Failed to fetch information');
      const results = await response.json();
      setInformationResults(results);
      setShowInformationPopup(true);

      toast({
        title: 'Information Search Complete',
        description: `Found ${results.length} results for "${query}"`,
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Search Error',
        description: 'Failed to search for information',
        variant: 'destructive',
      });
    }
  }, [toast]);



  // Voice event listeners
  useEffect(() => {
    const handleVoiceStatus = (event: CustomEvent) => {
      const { status } = event.detail;

      // Update robot state based on voice status
      if (status === 'listening' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('search');
      } else if (status === 'speaking' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('talk');
      } else if (status === 'idle' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('idle');
      }
    };

    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse } = event.detail;

      if (userQuery) {
        toast({
          title: 'Voice Command Received',
          description: `"${userQuery}"`,
        });
      }

      if (aiResponse) {
        // In fullscreen mode, show enhanced AI responses
        if (isFullScreenMode) {
          toast({
            title: 'Daswos AI Response',
            description: typeof aiResponse === 'string' ? aiResponse : 'AI response received',
            duration: 5000,
          });
        }
      }
    };

    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query && isFullScreenMode) {
        // In fullscreen mode, show product display with search results
        setCurrentSearchQuery(query);
        setShowProductDisplay(true);

        // Search initiated - no popup needed
      }
    };

    const handleAIAutoshop = () => {
      if (isFullScreenMode) {
        // Show product display with general recommendations
        setCurrentSearchQuery('recommended products');
        setShowProductDisplay(true);

        // AutoShop activated - no popup needed
      }
    };

    // Add event listeners
    window.addEventListener('voiceStatus', handleVoiceStatus as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);

    return () => {
      window.removeEventListener('voiceStatus', handleVoiceStatus as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    };
  }, [isFullScreenMode, toast]);

  return (
    <div className={className}>
      {/* Show full robot interface when active */}
      {isRobotActive && (
        <>
          {/* Robot Animation Layer - Hidden when product popup is open, but visible during guess results */}
          <RobotAnimation
            ref={robotAnimationRef}
            onRobotStateChange={handleRobotStateChange}
            isVisible={isRobotActive && !isProductPopupOpen && (lightsOn || showGuessResult)}
            isGlidingAway={isRobotGlidingAway}
            isFullScreenMode={isFullScreenMode}
            showGuessResult={showGuessResult}
          />

          {/* Robot Controls - hide when gliding away or when controls are closed */}
          {!isRobotGlidingAway && showRobotControls && (
            <RobotControls
              robotState={robotState}
              robotScale={robotScale}
              onStateChange={handleStateChange}
              onScaleChange={handleScaleChange}
              onCenter={handleCenter}
              onRoll={handleRoll}
              onClose={handleCloseRobotControls}
              onFullScreenToggle={handleFullScreenToggle}
              isFullScreenMode={isFullScreenMode}
              onGoAway={handleGoAway}
            />
          )}

          {/* Search Bar - show when search is activated */}
          {showSearchBar && !isRobotGlidingAway && (
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[70] w-96">
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg">
                <div className="flex items-center gap-2 mb-3">
                  <Search className="h-5 w-5 text-gray-500" />
                  <h3 className="text-lg font-semibold">Search Products & Information</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSearchBar(false)}
                    className="ml-auto h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Search for products or information..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={currentSearchQuery}
                    onChange={(e) => setCurrentSearchQuery(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        // Default to product search on Enter
                        setShowProductDisplay(true);
                        if (!isFullScreenMode) {
                          handleFullScreenToggle();
                        }
                      }
                    }}
                    autoFocus
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        setShowProductDisplay(true);
                        // Keep search bar open after search
                        if (!isFullScreenMode) {
                          handleFullScreenToggle();
                        }
                      }}
                      className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700"
                    >
                      Search Products
                    </Button>
                    <Button
                      onClick={() => {
                        handleInformationSearch(currentSearchQuery);
                        // Keep search bar open after search
                      }}
                      variant="outline"
                      className="flex-1 px-4 py-2"
                    >
                      Search Information
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Information Search Popup */}
          {showInformationPopup && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[80] flex items-center justify-center p-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-xl font-semibold">Information Search Results</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowInformationPopup(false)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-4 overflow-y-auto max-h-[60vh]">
                  {informationResults.length > 0 ? (
                    <div className="space-y-4">
                      {informationResults.map((result, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <h3 className="font-semibold text-lg mb-2">{result.title}</h3>
                          <p className="text-gray-600 dark:text-gray-400 mb-2">{result.description}</p>
                          {result.url && (
                            <a
                              href={result.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                            >
                              Read more →
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">No information found for your search.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

        </>
      )}

      {/* Fullscreen Product Display - shows when robot is in full screen mode */}
      <FullscreenProductDisplay
        isVisible={isFullScreenMode}
        searchQuery={currentSearchQuery}
        onClose={handleCloseProductDisplay}
        onClearSearch={handleClearSearch}
        onProductPopupChange={setIsProductPopupOpen}
        onGoAway={handleGoAway}
        robotPosition={robotPosition}
        onRobotPositionChange={(position) => {
          if (position) {
            setRobotPosition(position);
            // Also update the robot animation position
            if (robotAnimationRef.current) {
              // Use setPosition to avoid changing center position for guessing game
              robotAnimationRef.current.setPosition(position.x, position.y);
            }
          }
        }}
        lightsOn={lightsOn}
        onLightsChange={handleLightsChange}
        onGuessMode={handleGuessMode}
        isGuessMode={isGuessMode}
        userGuess={userGuess}
        lastRobotPosition={lastRobotPosition}
        showGuessResult={showGuessResult}
        onScreenClick={handleScreenClick}
      />

      {/* Guess Result Popup - Completely Transparent Background with Smart Positioning */}
      {showGuessResult && (
        <div className="fixed inset-0 z-[2000] pointer-events-none">
          <div
            className="absolute bg-transparent border-none rounded-lg p-4 max-w-xs pointer-events-auto"
            style={{
              // Smart positioning to avoid robot overlap
              top: (() => {
                if (!robotPosition) {
                  console.log('🎯 No robot position - centering result message');
                  return '50%';
                }

                console.log('🎯 Robot detected at:', robotPosition, 'moving result message');

                // If robot is in bottom half, move message to top
                if (robotPosition.y > (window.innerHeight || 800) / 2) {
                  console.log('🎯 Moving result message to top (robot in bottom)');
                  return '20%';
                } else {
                  console.log('🎯 Moving result message to bottom (robot in top)');
                  return '80%';
                }
              })(),
              left: (() => {
                if (!robotPosition) {
                  return '50%';
                }

                // If robot is in right half, move message to left
                if (robotPosition.x > (window.innerWidth || 1200) / 2) {
                  console.log('🎯 Moving result message to left (robot in right)');
                  return '20%';
                } else {
                  console.log('🎯 Moving result message to right (robot in left)');
                  return '80%';
                }
              })(),
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div className="text-center">
              <div className="text-3xl mb-2">
                {guessWasCorrect ? '🎉' : '🎯'}
              </div>
              <h2 className="text-white text-lg font-bold mb-1 drop-shadow-lg">
                {guessWasCorrect ? 'Congratulations!' : 'Nice Try!'}
              </h2>
              <p className="text-white text-sm mb-2 drop-shadow-lg">
                {guessWasCorrect
                  ? `You guessed correctly! Daswos appeared within ${guessDistance}px of your guess!`
                  : `You were ${guessDistance}px away from where Daswos appeared.`
                }
              </p>
              <p className="text-white text-xs mb-3 drop-shadow-lg">
                {guessWasCorrect
                  ? 'You have excellent prediction skills! 🌟'
                  : 'Try again next time! The target zone is 100px radius.'
                }
              </p>
              <button
                onClick={() => setShowGuessResult(false)}
                className="bg-white/30 hover:bg-white/40 text-white px-4 py-1 rounded text-sm transition-colors drop-shadow-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RobotOverlay;
